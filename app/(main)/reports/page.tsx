"use client";

import { But<PERSON> } from '@/app/components/forms/Button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { DatePicker } from '@/app/components/layout/calendar/date-picker';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import Header from '@/app/components/layout/Header';
import { Popover, PopoverContent, PopoverTrigger } from '@/app/components/navigation/popover';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/navigation/tabs';
import { useTheme } from '@/app/contexts/ThemeContext';
import { asApiResponse, asReportsResponse, extractApiError, hasApiError } from '@/app/types/api-responses';
import { getApiUrl } from '@/app/utils/env';
import { format } from 'date-fns';
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import {
    AlertTriangle,
    BarChart3,
    Calendar,
    ChevronDown,
    Download,
    LineChart as LineChartIcon,
    Loader2,
    <PERSON><PERSON>hart as PieChartIcon,
    RefreshCw
} from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import React, { Suspense, useEffect, useState } from 'react';
import { Bar, BarChart, CartesianGrid, Cell, Legend, Line, LineChart, Pie, PieChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';
import * as XLSX from 'xlsx';

// Define interfaces for report data
interface ReportType {
  id: string;
  name: string;
  description: string;
  endpoint: string;
}

interface InventorySummary {
  totalItems: number;
  totalStock: number;
  lowStockCount: number;
  outOfStockCount: number;
}

interface ProductionSummary {
  totalWorkOrders: number;
  pendingCount: number;
  inProgressCount: number;
  completedCount: number;
  overdueCount: number;
}

interface ProcurementSummary {
  totalPOs: number;
  pendingCount: number;
  receivedCount: number;
  totalSpend: number;
}

interface AssemblySummary {
  totalAssemblies: number;
  completableCount: number;
  incompleteCount: number;
}

interface ReportData {
  summary: InventorySummary | ProductionSummary | ProcurementSummary | AssemblySummary;
  generatedAt: string;
  [key: string]: any;
}

// Define theme-aware chart colors using CSS variables
const getChartColors = () => [
  'hsl(var(--chart-1))',
  'hsl(var(--chart-2))',
  'hsl(var(--chart-3))',
  'hsl(var(--chart-4))',
  'hsl(var(--chart-5))',
  'hsl(var(--chart-6))',
  'hsl(var(--chart-7))',
  'hsl(var(--chart-8))',
];

/**
 * Reports content component that displays various charts and analytics
 * Uses searchParams to determine initial report type
 */
const ReportsContent: React.FC = () => {
  const { theme } = useTheme();
  const searchParams = useSearchParams();
  const [reportTypes, setReportTypes] = useState<ReportType[]>([]);
  const [selectedReportType, setSelectedReportType] = useState<string>('inventory');
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [lowStockOnly, setLowStockOnly] = useState<boolean>(false);
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'line'>('bar');

  // Fetch report types on component mount
  useEffect(() => {
    fetchReportTypes();
  }, []);

  // Fetch report data when report type or filters change
  useEffect(() => {
    if (selectedReportType) {
      console.log(`--- Debug: useEffect triggering fetchReportData for ${selectedReportType} ---`);
      fetchReportData();
    }
  }, [selectedReportType, startDate, endDate, statusFilter, categoryFilter, lowStockOnly]);

  // Check for export mode in URL
  useEffect(() => {
    const initialMode = searchParams ? searchParams.get('mode') : null;
    const exportFormat = searchParams ? searchParams.get('format') : null;

    if (initialMode === 'export') {
      if (exportFormat && ['csv', 'excel', 'pdf'].includes(exportFormat)) {
        handleExportReport(exportFormat as 'csv' | 'excel' | 'pdf');
      } else {
        handleExportReport('csv'); // Default to CSV if no format specified
      }
    }
  }, [searchParams]);

  // Fetch available report types
  const fetchReportTypes = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(getApiUrl('/api/reports'));

      if (!response.ok) {
        throw new Error(`Error fetching report types: ${response.status}`);
      }

      const data = asApiResponse<ReportType[]>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setReportTypes(data.data || []);

      // Set initial report type if available
      if (data.data && data.data.length > 0) {
        const initialType = searchParams?.get('type') || data.data[0]?.id;
        setSelectedReportType(initialType || '');
      }
    } catch (err) {
      console.error('Error fetching report types:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch report data based on selected type and filters
  const fetchReportData = async () => {
    console.log(`--- Debug: fetchReportData started for report type: ${selectedReportType} ---`);
    console.log('Current selectedReportType:', selectedReportType, '| type:', typeof selectedReportType);
    console.log('Current startDate:', startDate, '| type:', typeof startDate);
    console.log('Current endDate:', endDate, '| type:', typeof endDate);
    console.log('Current statusFilter:', statusFilter, '| type:', typeof statusFilter);
    console.log('Current categoryFilter:', categoryFilter, '| type:', typeof categoryFilter);
    console.log('Current lowStockOnly:', lowStockOnly, '| type:', typeof lowStockOnly);

    if (!selectedReportType || typeof selectedReportType !== 'string' || selectedReportType.trim() === "") {
      console.error("CRITICAL_ERROR: selectedReportType is invalid or empty. Aborting fetch.", selectedReportType);
      setError("Invalid report type specified. Cannot fetch data.");
      setIsLoading(false);
      return; 
    }

    try {
      setIsLoading(true);
      setError(null);

      // Build query parameters
      const params = new URLSearchParams();

      if (startDate && endDate) {
        console.log('[fetchReportData] Appending startDate:', startDate.toISOString());
        params.append('startDate', startDate.toISOString());
        console.log('[fetchReportData] Appending endDate:', endDate.toISOString());
        params.append('endDate', endDate.toISOString());
      }

      if (statusFilter !== 'all') {
        console.log('[fetchReportData] Appending status:', statusFilter);
        params.append('status', statusFilter);
      }

      if (categoryFilter !== 'all') {
        console.log('[fetchReportData] Appending category:', categoryFilter);
        params.append('category', categoryFilter);
      }

      if (lowStockOnly) {
        console.log('[fetchReportData] Appending lowStock: true');
        params.append('lowStock', 'true');
      }

      const queryString = params.toString();
      const urlToFetch = `/api/reports/${selectedReportType}?${queryString}`;
      console.log('[fetchReportData] Attempting to fetch FINAL URL:', urlToFetch);

      const response = await fetch(urlToFetch);

      if (!response.ok) {
        throw new Error(`Error fetching report data: ${response.status}`);
      }

      const data = asReportsResponse(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setReportData((data.data as unknown as ReportData) || null);
    } catch (err) {
      console.error('Error fetching report data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      setReportData(null);
    } finally {
      setIsLoading(false);
    }
  };

  const valueData = reportData?.inventoryValueByCategory 
    ? Object.entries(reportData.inventoryValueByCategory).map(([name, value]) => ({
        name,
        value: typeof value === 'number' ? Number(value.toFixed(2)) : 0
      }))
    : [];

  // Use the same theme-aware chart colors function
  const chartColors = getChartColors();

  // Export report data in various formats
  const handleExportReport = (exportFormat: 'csv' | 'excel' | 'pdf' = 'csv') => {
    if (!reportData) {
      console.warn("No data available to export for the selected report type.");
      alert("No data available to export for this report type.");
      return;
    }

    let exportData: any[] = [];
    let headers: string[] = [];
    let title = getReportTitle();

    // Prepare data based on report type
    switch (selectedReportType) {
      case 'inventory':
        if (reportData.lowStockItems) {
          headers = ['Part ID', 'Name', 'Current Stock', 'Reorder Level'];
          exportData = reportData.lowStockItems.map((item: any) => [
            String(item._id || 'N/A'),
            String(item.name || 'N/A'),
            String(item.inventory?.current_stock ?? 0),
            String(item.reorder_level ?? 0)
          ]);
        }
        break;
      case 'production':
        if (reportData.pendingWorkOrders) {
          headers = ['Work Order ID', 'Product', 'Quantity', 'Status', 'Due Date'];
          exportData = reportData.pendingWorkOrders.map((item: any) => [
            String(item.woNumber || 'N/A'),
            String(item.productId?.name || item.productId || 'N/A'),
            String(item.quantity ?? 0),
            String(item.status || 'N/A'),
            String(item.dueDate ? format(new Date(item.dueDate), 'yyyy-MM-dd') : 'N/A')
          ]);
        }
        break;
      case 'procurement':
        if (reportData.pendingPOs) {
          headers = ['PO Number', 'Supplier', 'Total Amount', 'Status', 'Order Date'];
          exportData = reportData.pendingPOs.map((item: any) => [
            String(item.poNumber || 'N/A'),
            String(item.supplierId?.name || item.supplierId || 'N/A'),
            String((typeof item.totalAmount === 'number' && !isNaN(item.totalAmount)) ? item.totalAmount.toFixed(2) : '0.00'),
            String(item.status || 'N/A'),
            String(item.orderDate ? format(new Date(item.orderDate), 'yyyy-MM-dd') : 'N/A')
          ]);
        }
        break;
      case 'assembly':
        if (reportData.assemblyStatus) {
          headers = ['Assembly Code', 'Name', 'Can Complete', 'Missing Parts'];
          exportData = reportData.assemblyStatus.map((item: any) => [
            String(item.assemblyCode || 'N/A'),
            String(item.name || 'N/A'),
            String(item.canComplete ? 'Yes' : 'No'),
            String(item.missingParts && item.missingParts.length > 0 ? item.missingParts.map((p: any) => String(p.name || 'Unknown Part')).join('; ') : 'None')
          ]);
        }
        break;
      default:
        break;
    }

    if (exportData.length === 0) {
      console.warn("No data available to export for the selected report type.");
      alert("No data available to export for this report type.");
      return;
    }

    const fileName = `${selectedReportType}_report_${new Date().toISOString().split('T')[0]}`;

    if (exportFormat === 'csv') {
      // Create CSV content
      const csvContent = [
        headers.join(','),
        ...exportData.map(row => row.join(','))
      ].join('\n');

      // Create and download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `${fileName}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    }
    else if (exportFormat === 'excel') {
      // Create worksheet
      const ws = XLSX.utils.aoa_to_sheet([headers, ...exportData]);

      // Create workbook
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, title);

      // Generate Excel file and trigger download
      XLSX.writeFile(wb, `${fileName}.xlsx`);
    }
    else if (exportFormat === 'pdf') {
      // Create PDF document
      const doc = new jsPDF();

      // Add title
      doc.setFontSize(16);
      doc.text(title, 14, 15);

      // Add generation date
      doc.setFontSize(10);
      const generatedDate = reportData.generatedAt
        ? new Date(reportData.generatedAt).toLocaleString()
        : new Date().toLocaleString();
      doc.text(`Generated: ${generatedDate}`, 14, 22);

      // Initialize yPos variable
      let yPos = 30;

      // Add summary information if available
      if (reportData.summary) {
        doc.setFontSize(12);
        doc.text('Summary', 14, 30);

        yPos = 35;
        const summaryData = [];

        switch (selectedReportType) {
          case 'inventory':
            const invSummary = reportData.summary as InventorySummary;
            summaryData.push(
              [`Total Items: ${invSummary.totalItems}`],
              [`Total Stock: ${invSummary.totalStock}`],
              [`Low Stock Items: ${invSummary.lowStockCount}`],
              [`Out of Stock: ${invSummary.outOfStockCount}`]
            );
            break;
          case 'production':
            const prodSummary = reportData.summary as ProductionSummary;
            summaryData.push(
              [`Total Work Orders: ${prodSummary.totalWorkOrders}`],
              [`Pending: ${prodSummary.pendingCount}`],
              [`In Progress: ${prodSummary.inProgressCount}`],
              [`Completed: ${prodSummary.completedCount}`],
              [`Overdue: ${prodSummary.overdueCount}`]
            );
            break;
          case 'procurement':
            const procSummary = reportData.summary as ProcurementSummary;
            summaryData.push(
              [`Total Purchase Orders: ${procSummary.totalPOs}`],
              [`Pending: ${procSummary.pendingCount}`],
              [`Received: ${procSummary.receivedCount}`],
              [`Total Spend: $${(procSummary.totalSpend || 0).toLocaleString()}`]
            );
            break;
          case 'assembly':
            const asmSummary = reportData.summary as AssemblySummary;
            summaryData.push(
              [`Total Assemblies: ${asmSummary.totalAssemblies}`],
              [`Completable: ${asmSummary.completableCount}`],
              [`Incomplete: ${asmSummary.incompleteCount}`]
            );
            break;
        }

        // Add summary data
        summaryData.forEach(item => {
          doc.text(item[0] || '', 14, yPos);
          yPos += 5;
        });

        yPos += 5;
      }

      // Add table
      (doc as any).autoTable({
        head: [headers],
        body: exportData,
        startY: yPos,
        theme: 'grid',
        styles: {
          fontSize: 10,
          cellPadding: 3,
          lineColor: [200, 200, 200],
          lineWidth: 0.1,
        },
        headStyles: {
          fillColor: [41, 128, 185],
          textColor: 255,
          fontStyle: 'bold',
        },
        alternateRowStyles: {
          fillColor: [240, 240, 240],
        },
      });

      // Save PDF
      doc.save(`${fileName}.pdf`);
    }

    alert(`Report exported in ${exportFormat.toUpperCase()} format successfully`);
  };

  // Get chart data based on report type
  const getChartData = () => {
    if (!reportData) return [];

    switch (selectedReportType) {
      case 'inventory':
        // Create data for inventory levels by category
        if (reportData.lowStockItems) {
          const categoryMap = new Map<string, number>();

          reportData.lowStockItems.forEach((item: any) => {
            const category = item.category || 'Uncategorized';
            categoryMap.set(category, (categoryMap.get(category) || 0) + 1);
          });

          return Array.from(categoryMap.entries()).map(([name, value]) => ({ name, value }));
        }
        break;
      case 'production':
        // Create data for work order status distribution
        if (reportData.summary) {
          const summary = reportData.summary as ProductionSummary;
          return [
            { name: 'Pending', value: summary.pendingCount },
            { name: 'In Progress', value: summary.inProgressCount },
            { name: 'Completed', value: summary.completedCount },
            { name: 'Overdue', value: summary.overdueCount }
          ];
        }
        break;
      case 'procurement':
        // Create data for purchase order status
        if (reportData.summary) {
          const summary = reportData.summary as ProcurementSummary;
          return [
            { name: 'Pending', value: summary.pendingCount },
            { name: 'Received', value: summary.receivedCount }
          ];
        }
        break;
      case 'assembly':
        // Create data for assembly completability
        if (reportData.summary) {
          const summary = reportData.summary as AssemblySummary;
          return [
            { name: 'Completable', value: summary.completableCount },
            { name: 'Incomplete', value: summary.incompleteCount }
          ];
        }
        break;
      default:
        return [];
    }

    return [];
  };

  // Get report title based on selected type
  const getReportTitle = () => {
    const reportType = reportTypes.find(type => type.id === selectedReportType);
    return reportType ? reportType.name : 'Report';
  };

  // Get report description based on selected type
  const getReportDescription = () => {
    const reportType = reportTypes.find(type => type.id === selectedReportType);
    return reportType ? reportType.description : '';
  };

  // Get summary cards based on report type
  const getSummaryCards = () => {
    if (!reportData || !reportData.summary) return null;

    switch (selectedReportType) {
      case 'inventory':
        const inventorySummary = reportData.summary as InventorySummary;
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{inventorySummary.totalItems}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Stock</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{inventorySummary.totalStock}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Low Stock Items</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-warning">{inventorySummary.lowStockCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Out of Stock</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-destructive">{inventorySummary.outOfStockCount}</div>
              </CardContent>
            </Card>
          </div>
        );
      case 'production':
        const productionSummary = reportData.summary as ProductionSummary;
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Work Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{productionSummary.totalWorkOrders}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-info">{productionSummary.pendingCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-warning">{productionSummary.inProgressCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Completed</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-success">{productionSummary.completedCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Overdue</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-destructive">{productionSummary.overdueCount}</div>
              </CardContent>
            </Card>
          </div>
        );
      case 'procurement':
        const procurementSummary = reportData.summary as ProcurementSummary;
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Purchase Orders</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{procurementSummary.totalPOs}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Pending</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-warning">{procurementSummary.pendingCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Received</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-success">{procurementSummary.receivedCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Spend</CardTitle>
                {/* Ensure procurementSummary and totalSpend are valid before calling toFixed */}
                <CardTitle className="text-4xl">
                  ${procurementSummary && typeof procurementSummary.totalSpend === 'number' ? procurementSummary.totalSpend.toFixed(2) : '0.00'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">${procurementSummary && typeof procurementSummary.totalSpend === 'number' ? procurementSummary.totalSpend.toFixed(2) : '0.00'}</div>
              </CardContent>
            </Card>
          </div>
        );
      case 'assembly':
        const assemblySummary = reportData.summary as AssemblySummary;
        return (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Total Assemblies</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{assemblySummary.totalAssemblies}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Completable</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-success">{assemblySummary.completableCount}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Incomplete</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-destructive">{assemblySummary.incompleteCount}</div>
              </CardContent>
            </Card>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="Reports" />

      <div className="px-8 pb-8">
        {isLoading && !reportData ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading reports...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-red-700 dark:text-red-400">
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              <p>{error}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => {
                setError(null);
                fetchReportTypes();
              }}
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Retry
            </Button>
          </div>
        ) : (
          <>
            {/* Report Type Selection */}
            <div className="mb-6">
              <Tabs
                defaultValue={selectedReportType}
                value={selectedReportType}
                onValueChange={(value) => setSelectedReportType(value)}
                className="w-full"
              >
                <div className="flex justify-between items-center mb-4">
                  <TabsList>
                    {reportTypes.map((type) => (
                      <TabsTrigger key={type.id} value={type.id}>
                        {type.name}
                      </TabsTrigger>
                    ))}
                  </TabsList>

                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={fetchReportData}
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Refresh
                    </Button>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="default"
                          size="sm"
                          disabled={!reportData}
                        >
                          <Download className="w-4 h-4 mr-2" />
                          Export
                          <ChevronDown className="w-3 h-3 ml-2" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-48 p-0">
                        <div className="py-1">
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExportReport('csv')}
                          >
                            Export as CSV
                          </Button>
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExportReport('excel')}
                          >
                            Export as Excel
                          </Button>
                          <Button
                            variant="ghost"
                            className="w-full justify-start rounded-none"
                            onClick={() => handleExportReport('pdf')}
                          >
                            Export as PDF
                          </Button>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>

                {/* Report Content */}
                {reportTypes.map((type) => (
                  <TabsContent key={type.id} value={type.id} className="mt-0">
                    <Card>
                      <CardHeader>
                        <CardTitle>{getReportTitle()}</CardTitle>
                        <CardDescription>{getReportDescription()}</CardDescription>
                      </CardHeader>
                      <CardContent>
                        {/* Filters */}
                        <div className="flex flex-wrap gap-4 mb-6">
                          {(selectedReportType === 'inventory' || selectedReportType === 'production' || selectedReportType === 'procurement') && (
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <DatePicker
                                date={startDate}
                                setDate={(date) => setStartDate(date)}
                                placeholder="Start Date"
                              />
                              <span>to</span>
                              <DatePicker
                                date={endDate}
                                setDate={(date) => setEndDate(date)}
                                placeholder="End Date"
                              />
                            </div>
                          )}

                          {selectedReportType === 'inventory' && (
                            <div className="flex items-center space-x-2">
                              <input
                                type="checkbox"
                                id="lowStockOnly"
                                checked={lowStockOnly}
                                onChange={(e) => setLowStockOnly(e.target.checked)}
                                className="rounded border-gray-300"
                              />
                              <label htmlFor="lowStockOnly" className="text-sm">
                                Low Stock Only
                              </label>
                            </div>
                          )}

                          {(selectedReportType === 'production' || selectedReportType === 'procurement') && (
                            <Select
                              value={statusFilter}
                              onValueChange={setStatusFilter}
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue placeholder="Status" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All Statuses</SelectItem>
                                {selectedReportType === 'production' ? (
                                  <>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="in_progress">In Progress</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                  </>
                                ) : (
                                  <>
                                    <SelectItem value="pending">Pending</SelectItem>
                                    <SelectItem value="ordered">Ordered</SelectItem>
                                    <SelectItem value="received">Received</SelectItem>
                                  </>
                                )}
                              </SelectContent>
                            </Select>
                          )}
                        </div>

                        {/* Summary Cards */}
                        {getSummaryCards()}

                        {/* Chart Type Selection */}
                        <div className="flex justify-end mb-4">
                          <div className="flex border rounded-md overflow-hidden">
                            <Button
                              variant={chartType === 'bar' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('bar')}
                              className="rounded-none"
                            >
                              <BarChart3 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={chartType === 'pie' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('pie')}
                              className="rounded-none"
                            >
                              <PieChartIcon className="h-4 w-4" />
                            </Button>
                            <Button
                              variant={chartType === 'line' ? 'default' : 'ghost'}
                              size="sm"
                              onClick={() => setChartType('line')}
                              className="rounded-none"
                            >
                              <LineChartIcon className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        {/* Chart */}
                        {isLoading ? (
                          <div className="flex justify-center items-center h-64">
                            <Loader2 className="h-8 w-8 animate-spin text-primary" />
                            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading data...</span>
                          </div>
                        ) : reportData ? (
                          <div className="h-96">
                            {getChartData().length > 0 ? (
                              <ResponsiveContainer width="100%" height="100%">
                                {chartType === 'bar' ? (
                                  <BarChart
                                    data={getChartData()}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke={theme.mode === 'dark' ? '#444' : '#ddd'} />
                                    <XAxis dataKey="name" tick={{ fill: theme.mode === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <YAxis tick={{ fill: theme.mode === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <Tooltip
                                      contentStyle={{
                                        backgroundColor: theme.mode === 'dark' ? '#333' : '#fff',
                                        borderColor: theme.mode === 'dark' ? '#444' : '#ccc',
                                        color: theme.mode === 'dark' ? '#F0F0F0' : '#333'
                                      }}
                                    />
                                    <Legend />
                                    <Bar dataKey="value" name="Value" fill="#3B82F6" />
                                  </BarChart>
                                ) : chartType === 'pie' ? (
                                  <PieChart>
                                    <Pie
                                      data={getChartData()}
                                      cx="50%"
                                      cy="50%"
                                      labelLine={false}
                                      outerRadius={150}
                                      fill="#8884d8"
                                      dataKey="value"
                                      label={({ name, percent }: { name: string; percent: number }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                                    >
                                      {getChartData().map((entry, index) => (
                                        <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                                      ))}
                                    </Pie>
                                    <Tooltip
                                      contentStyle={{
                                        backgroundColor: theme.mode === 'dark' ? '#333' : '#fff',
                                        borderColor: theme.mode === 'dark' ? '#444' : '#ccc',
                                        color: theme.mode === 'dark' ? '#F0F0F0' : '#333'
                                      }}
                                    />
                                    <Legend />
                                  </PieChart>
                                ) : (
                                  <LineChart
                                    data={getChartData()}
                                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                                  >
                                    <CartesianGrid strokeDasharray="3 3" stroke={theme.mode === 'dark' ? '#444' : '#ddd'} />
                                    <XAxis dataKey="name" tick={{ fill: theme.mode === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <YAxis tick={{ fill: theme.mode === 'dark' ? '#A0A0A0' : '#333' }} />
                                    <Tooltip
                                      contentStyle={{
                                        backgroundColor: theme.mode === 'dark' ? '#333' : '#fff',
                                        borderColor: theme.mode === 'dark' ? '#444' : '#ccc',
                                        color: theme.mode === 'dark' ? '#F0F0F0' : '#333'
                                      }}
                                    />
                                    <Legend />
                                    <Line type="monotone" dataKey="value" name="Value" stroke="#3B82F6" />
                                  </LineChart>
                                )}
                              </ResponsiveContainer>
                            ) : (
                              <div className="flex items-center justify-center h-full">
                                <p className="text-gray-500 dark:text-gray-400">No data available for this report.</p>
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="flex items-center justify-center h-64">
                            <p className="text-gray-500 dark:text-gray-400">Select a report type to view data.</p>
                          </div>
                        )}
                      </CardContent>
                      <CardFooter className="text-sm text-muted-foreground">
                        {reportData && reportData.generatedAt && (
                          <div>
                            Report generated at: {format(new Date(reportData.generatedAt), 'MMM dd, yyyy HH:mm:ss')}
                          </div>
                        )}
                      </CardFooter>
                    </Card>
                  </TabsContent>
                ))}
              </Tabs>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

// Main Reports component that wraps the content in a Suspense boundary
const Reports: React.FC = () => {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center h-screen w-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-yellow-500"></div>
      </div>
    }>
      <ReportsContent />
    </Suspense>
  );
};

export default Reports;
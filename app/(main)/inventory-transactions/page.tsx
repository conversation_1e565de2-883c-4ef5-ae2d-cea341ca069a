"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/app/components/data-display/alert";
import { StockMovementForm } from '@/app/components/features/StockMovementForm';
import { Button } from "@/app/components/forms/Button";
import Header from '@/app/components/layout/Header';
import { ViewTransactionModal } from '@/app/components/modals/ViewTransactionModal';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { useAppContext } from '@/app/contexts/AppContext';
import { useTheme } from '@/app/contexts/ThemeContext';
import { type InventoryTransactionColumnData } from '@/app/components/data-display/data-table';
import { DataTableColumn } from '@/app/components/data-display/data-table/types';
import { format } from 'date-fns';
import {
    AlertTriangle,
    ArrowDownLeft,
    ArrowUpRight,
    Eye,
    FileText,
    Package,
    Plus
} from 'lucide-react';
import React, { useEffect, useState, useMemo } from 'react';
import { toast } from 'sonner';
import { generateTransactionId } from '@/app/services/transactionIdGenerator';

/**
 * Interface for raw API response data that may have populated objects
 */
interface RawInventoryTransaction {
  /** MongoDB ObjectId */
  _id?: string;
  /** Human-readable transaction ID (e.g., "SIP-20250712-001") */
  transactionId?: string;
  /** ID of the part involved in the transaction - can be string or populated object */
  partId: string | { _id: string; partNumber: string; name: string; };
  /** ID of the warehouse involved in the transaction - can be string or populated object */
  warehouseId: string | { _id: string; location_id: string; name: string; };
  /** Type of inventory transaction */
  transactionType?: string;
  /** Quantity changed in this transaction */
  quantity?: number;
  /** Previous stock level before transaction */
  previousStock?: number;
  /** New stock level after transaction */
  newStock?: number;
  /** Date when the transaction occurred */
  transactionDate?: string | Date;
  /** Reference number for the transaction */
  referenceNumber?: string | null;
  /** Reference type (e.g., PurchaseOrder, WorkOrder) */
  referenceType?: string | null;
  /** User who performed the transaction - can be string or populated object */
  userId?: string | { _id: string; username: string; fullName: string; } | null;
  /** Additional notes about the transaction */
  notes?: string | null;
  /** Creation timestamp */
  createdAt?: string | Date;
  /** Update timestamp */
  updatedAt?: string | Date;
}

/**
 * Interface for processed inventory transaction data
 * Aligned with the canonical schema defined in inventorytransaction.model.ts
 */
interface InventoryTransaction {
  /** MongoDB ObjectId */
  _id?: string;
  /** Human-readable transaction ID (e.g., "SIP-20250712-001") */
  transactionId?: string;
  /** ID of the part involved in the transaction */
  partId: string;
  /** ID of the warehouse involved in the transaction */
  warehouseId: string;
  /** Type of inventory transaction */
  transactionType: 'stock_in_purchase' | 'stock_out_production' | 'adjustment_cycle_count' | 'stock_in_production' | 'transfer_out' | 'transfer_in' | 'sales_shipment' | string;
  /** Quantity changed in this transaction */
  quantity: number;
  /** Previous stock level before transaction */
  previousStock: number;
  /** New stock level after transaction */
  newStock: number;
  /** Date when the transaction occurred */
  transactionDate: string | Date;
  /** Reference number for the transaction */
  referenceNumber?: string | null;
  /** Reference type (e.g., PurchaseOrder, WorkOrder) */
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | null;
  /** User who performed the transaction */
  userId: string;
  /** Additional notes about the transaction */
  notes?: string | null;
  /** Creation timestamp */
  createdAt?: string | Date;
  /** Update timestamp */
  updatedAt?: string | Date;

  // UI display fields populated from API response
  partName?: string;
  partNumber?: string;
  warehouseName?: string;
  warehouseLocationId?: string;
  userName?: string;

  // Legacy field mappings for backward compatibility
  type?: string; // Maps to transactionType
  quantityChanged?: number; // Maps to quantity
  stockOnHandBefore?: number; // Maps to previousStock
  stockOnHandAfter?: number; // Maps to newStock
  referenceModel?: string; // Maps to referenceType
  referenceId?: string; // Maps to referenceNumber
}

/**
 * Interface for product data
 */
interface Product {
  /** Unique identifier for the product */
  id: string;
  /** Name of the product */
  name: string;
}

/**
 * Interface for user data
 */
interface User {
  /** Unique identifier for the user */
  id: string;
  /** Name of the user */
  name: string;
}

/**
 * Inventory Transactions page component
 * Displays a list of inventory transactions and allows users to create new transactions
 * Supports filtering by transaction type and date range
 */
const InventoryTransactions: React.FC = () => {
  const { theme } = useTheme();
  const { currentLocation } = useAppContext();
  const [transactions, setTransactions] = useState<RawInventoryTransaction[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Transaction detail view modal state
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedTransaction, setSelectedTransaction] = useState<InventoryTransactionColumnData | null>(null);

  // Server-side pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [totalItems, setTotalItems] = useState(0);

  // Define columns for the StandardizedTable
  const columns = useMemo<DataTableColumn<InventoryTransactionColumnData>[]>(() => [
    {
      accessorKey: 'transactionId',
      header: 'Transaction ID',
      cell: ({ row }) => (
        <div className="font-medium">
          {row.original.transactionId || row.original._id.slice(-8)}
        </div>
      ),
    },
    {
      accessorKey: 'transactionType',
      header: 'Type',
      enableHiding: true, // Allow hiding transaction type
      cell: ({ row }) => {
        const type = row.original.transactionType;
        const typeColors = {
          stock_in: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
          stock_out: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
          adjustment: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
          transfer: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
        };
        const colorClass = typeColors[type as keyof typeof typeColors] || 'bg-gray-100 text-gray-800';
        return (
          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${colorClass}`}>
            {type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </span>
        );
      },
    },
    {
      accessorKey: 'itemName',
      header: 'Item',
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.itemName}</div>
          <div className="text-sm text-muted-foreground">{row.original.partNumber}</div>
        </div>
      ),
    },
    {
      accessorKey: 'quantity',
      header: 'Quantity',
      cell: ({ row }) => {
        const quantity = row.original.quantity;
        const isPositive = quantity > 0;
        return (
          <div className={`flex items-center gap-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {isPositive ? <ArrowUpRight size={14} /> : <ArrowDownLeft size={14} />}
            <span className="font-medium">{Math.abs(quantity)}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'warehouseName',
      header: 'Warehouse',
      cell: ({ row }) => (
        <div className="text-sm">{row.original.warehouseName}</div>
      ),
    },
    {
      accessorKey: 'transactionDate',
      header: 'Date',
      enableHiding: true, // Allow hiding date
      cell: ({ row }) => (
        <div className="text-sm">
          {format(new Date(row.original.transactionDate), 'MMM dd, yyyy')}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      enableHiding: false, // Always show actions
      cell: ({ row }) => (
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleViewTransaction(row.original)}
            className="h-8 w-8 p-0"
          >
            <Eye className="h-4 w-4" />
            <span className="sr-only">View transaction</span>
          </Button>
        </div>
      ),
    },
  ], []);

  // Helper function to validate and convert reference type
  const validateReferenceType = (value: any): 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | null => {
    const validTypes = ['PurchaseOrder', 'WorkOrder', 'SalesOrder', 'StockAdjustment'];
    if (typeof value === 'string' && validTypes.includes(value)) {
      return value as 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment';
    }
    return null;
  };

  // Fetch transactions with server-side pagination
  const fetchTransactions = async (page: number = currentPage, limit: number = pageSize) => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/inventory-transactions?page=${page}&limit=${limit}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch transactions');
      }

      const result = await response.json();
      setTransactions(result.data || []);
      setTotalItems(result.pagination?.total || 0);
      setCurrentPage(result.pagination?.page || page);
    } catch (error) {
      console.error('Error fetching transactions:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch transactions');
      toast.error('Could not load inventory transactions');
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch transactions on component mount and when pagination changes
  useEffect(() => {
    fetchTransactions(currentPage, pageSize);
  }, [currentPage, pageSize]);

  // Open the transaction form modal
  const openModal = () => {
    setIsModalOpen(true);
  };

  // Handle pagination changes - FIXED: Optimistic updates
  const handlePaginationChange = (pagination: { pageIndex: number; pageSize: number }) => {
    console.log('Inventory transactions pagination changed:', pagination);
    const newPage = pagination.pageIndex + 1; // Convert 0-based to 1-based
    const newPageSize = pagination.pageSize;

    // FIXED: Update state immediately for instant UI feedback
    if (newPageSize !== pageSize) {
      setPageSize(newPageSize);
      setCurrentPage(1); // Reset to first page when page size changes
      // Fetch data with new page size in the background
      fetchTransactions(1, newPageSize);
    } else if (newPage !== currentPage) {
      setCurrentPage(newPage);
      // Fetch data for new page in the background
      fetchTransactions(newPage, newPageSize);
    }
  };

  // Handle successful transaction creation
  const handleTransactionSuccess = () => {
    // Refresh transactions data instead of full page reload
    fetchTransactions(currentPage, pageSize);
    toast.success('Transaction created successfully');
  };

  // Performance: Memoize pagination object to prevent unnecessary re-renders
  const memoizedPagination = useMemo(() => ({
    pageIndex: currentPage - 1,
    pageSize: pageSize
  }), [currentPage, pageSize]);

  // Close the transaction form modal
  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Transform raw API transactions to include human-readable data
  const transformedTransactions = useMemo(() => {
    return transactions.map((transaction: any) => {
      // Extract part information
      const partName = typeof transaction.partId === 'object' && transaction.partId
        ? transaction.partId.name || ''
        : '';

      const partNumber = typeof transaction.partId === 'object' && transaction.partId
        ? transaction.partId.partNumber || ''
        : '';

      // Handle warehouse data
      const warehouseIdValue = typeof transaction.warehouseId === 'string'
        ? transaction.warehouseId
        : transaction.warehouseId?._id;

      const warehouseName = typeof transaction.warehouseId === 'object' && transaction.warehouseId
        ? transaction.warehouseId.name || transaction.warehouseId.location_id
        : '';

      const warehouseLocationId = typeof transaction.warehouseId === 'object' && transaction.warehouseId
        ? transaction.warehouseId.location_id || ''
        : '';

      // Handle user data
      const userName = typeof transaction.userId === 'object' && transaction.userId
        ? transaction.userId.fullName || transaction.userId.username || 'Unknown User'
        : transaction.userName || 'Unknown User';

      const userIdValue = typeof transaction.userId === 'string'
        ? transaction.userId
        : transaction.userId?._id;

      // Use existing transaction ID or generate a fallback
      const transactionId = transaction.transactionId || `TXN-${Date.now()}`;

      return {
        ...transaction,
        transactionId,
        partName,
        partNumber,
        warehouseName,
        warehouseLocationId,
        userName,
        partId: typeof transaction.partId === 'string' ? transaction.partId : transaction.partId?._id,
        warehouseId: warehouseIdValue,
        userId: userIdValue,
      };
    });
  }, [transactions]);

  // Transform InventoryTransaction to InventoryTransactionColumnData for the modal
  const transformTransactionForModal = (transaction: InventoryTransaction): InventoryTransactionColumnData => {
    return {
      _id: transaction._id || '',
      transactionId: transaction.transactionId || '', // Human-readable transaction ID
      transactionType: transaction.transactionType,
      itemName: transaction.partName || 'Unknown Part',
      itemId: transaction.partId,
      partNumber: transaction.partNumber || '', // Human-readable part number
      itemType: 'Part' as const,
      warehouseName: transaction.warehouseName || 'Unknown Warehouse',
      warehouseId: transaction.warehouseId,
      warehouseLocationId: transaction.warehouseLocationId || '', // Human-readable warehouse location ID
      quantity: transaction.quantity,
      previousStock: transaction.previousStock,
      newStock: transaction.newStock,
      transactionDate: transaction.transactionDate,
      referenceNumber: transaction.referenceNumber || '',
      referenceType: transaction.referenceType || '',
      reference: transaction.referenceNumber
        ? `${transaction.referenceType || 'Reference'}: ${transaction.referenceNumber}`
        : '',
      userName: transaction.userName || 'Unknown User',
      userId: transaction.userId, // Keep ObjectID for compatibility
      notes: transaction.notes || '',
    };
  };

  // Handle viewing transaction details
  const handleViewTransaction = (transaction: InventoryTransactionColumnData) => {
    setSelectedTransaction(transaction);
    setIsViewModalOpen(true);
  };



  // Close the transaction detail view modal
  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setSelectedTransaction(null);
  };



  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Inventory Transactions" />

      <div className="px-8 pb-8">
        {error && (
          <Alert variant="destructive" className="mb-6 border-red-200 dark:border-red-900/50 bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 shadow-sm">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            <AlertTitle className="font-semibold text-red-800 dark:text-red-200">Error</AlertTitle>
            <AlertDescription className="text-red-700 dark:text-red-300">{error}</AlertDescription>
          </Alert>
        )}

        {/* StandardizedTable with action button in horizontal layout */}
        <StandardizedTable
          data={transformedTransactions}
          columns={columns}
          searchPlaceholder="Search transactions..."
          viewOptions={[
            { id: 'table', label: 'Table', icon: '📋' }
          ]}
          renderActions={() => (
            <Button
              onClick={openModal}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <Plus size={16} />
              <span>New Transaction</span>
            </Button>
          )}
          // FIXED: Server-side pagination configuration using direct props and memoized pagination
          manualPagination={true}
          totalRows={totalItems}
          onPaginationChange={handlePaginationChange}
          initialPagination={memoizedPagination}
          enablePagination={true}
          enableSorting={true}
          enableFiltering={true}
          enableGlobalSearch={false} // Using StandardizedTable's search instead
          enableColumnVisibility={false}
          mobileDisplayMode="cards"
          density="normal"
          pageSizeOptions={[10, 20, 50, 100]}
          isLoading={isLoading}
          renderEmptyState={() => (
            <div className="text-center py-8">
              <Package className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <h3 className="text-lg font-medium mb-2">No Transactions Found</h3>
              <p className="text-sm text-muted-foreground mb-4">
                No inventory transactions found in the system.
              </p>
              <Button onClick={openModal}>
                <Plus size={16} className="mr-2" />
                Create Transaction
              </Button>
            </div>
          )}
        />
      </div>

      {/* Stock Movement Form Modal */}
      <StockMovementForm
        isOpen={isModalOpen}
        onClose={closeModal}
        onSuccess={handleTransactionSuccess}
      />

      {/* Transaction Detail View Modal */}
      {selectedTransaction && (
        <ViewTransactionModal
          transaction={selectedTransaction}
          isOpen={isViewModalOpen}
          onClose={closeViewModal}
        />
      )}
    </div>
  );
};

export default InventoryTransactions;
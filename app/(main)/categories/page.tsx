"use client";

import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { asApiResponse, extractApiError, hasApiError } from '@/app/types/api-responses';
import { getApiUrl } from '@/app/utils/apiUtils';
import { motion } from 'framer-motion';
import { ChevronDown, Edit, Loader2, Package, Plus, Search, Trash2 } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { showNetworkErrorToast, showErrorToast, showSuccessToast } from '@/app/components/feedback';
import Header from '../../../app/components/layout/Header';
import { useTheme } from '../../contexts/ThemeContext';

// Define interfaces for category data
interface Part {
  _id: string;
  name: string;
}

interface Category {
  _id: string;
  name: string;
  description?: string;
  parentCategory?: {
    _id: string;
    name: string;
  };
  parts?: Part[];
}
// Modal interfaces
interface CategoryFormData {
  name: string;
  description?: string;
  parentCategory?: string;
}

const Categories: React.FC = () => {
  const { theme } = useTheme();
  const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
  const [categories, setCategories] = useState<Category[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [formData, setFormData] = useState<CategoryFormData>({
    name: '',
    description: '',
    parentCategory: ''
  });
  const [categoryParts, setCategoryParts] = useState<Record<string, Part[]>>({});
  const [loadingParts, setLoadingParts] = useState<Record<string, boolean>>({});

  // Fetch categories on component mount
  useEffect(() => {
    fetchCategoriesAndParts();
  }, []);

  // Fetch categories and their parts from API
  const fetchCategoriesAndParts = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Request categories with their parts included
      const response = await fetch(getApiUrl('/api/categories?includeParts=true&partsLimit=5')); // Fetch top 5 parts per category

      if (!response.ok) {
        throw new Error(`Error fetching categories and parts: ${response.status}`);
      }

      const data = asApiResponse<Category[]>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setCategories(data.data || []);
    } catch (err) {
      console.error('Failed to fetch categories and parts:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      showNetworkErrorToast(fetchCategoriesAndParts, { customMessage: 'Failed to load categories and their parts' });
    } finally {
      setIsLoading(false);
    }
  };

  // Note: The fetchPartsForCategory function might still be useful for lazy loading more parts if needed,
  // but initial parts are now fetched with categories.
  const fetchPartsForCategory = async (categoryId: string) => {
    // This function is now primarily for loading *more* parts if a category is expanded further,
    // or if parts weren't loaded initially.
    // Check if parts for this category are already in the main categories state or categoryParts state
    const categoryData = categories.find(c => c._id === categoryId);
    if ((categoryData && categoryData.parts && categoryData.parts.length > 0) || categoryParts[categoryId] || loadingParts[categoryId]) {
      // If parts are already in the main category object or explicitly fetched, no need to fetch again unless forced
      // or implementing pagination for parts list here.
      return;
    }

    setLoadingParts(prev => ({ ...prev, [categoryId]: true }));

    try {
      // Example: Fetch parts if not initially loaded or for pagination
      const response = await fetch(`/api/parts?category=${categoryId}&limit=10`); // Fetch up to 10 parts

      if (!response.ok) {
        throw new Error(`Error fetching parts: ${response.status}`);
      }

      const data = asApiResponse<Part[]>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setCategoryParts(prev => ({
        ...prev,
        [categoryId]: data.data || [] // Store fetched parts separately or merge if needed
      }));
    } catch (err) {
      console.error(`Failed to fetch parts for category ${categoryId}:`, err);
      showNetworkErrorToast(() => fetchPartsForCategory(categoryId), { customMessage: 'Failed to load parts for this category' });
    } finally {
      setLoadingParts(prev => ({ ...prev, [categoryId]: false }));
    }
  };

  // Add a new category
  const addCategory = async () => {
    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`Error adding category: ${response.status}`);
      }

      const data = asApiResponse<Category>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      showSuccessToast('Category added successfully');
      setShowAddModal(false);
      setFormData({ name: '', description: '', parentCategory: '' });
      fetchCategoriesAndParts(); // Refresh the list
    } catch (err) {
      console.error('Failed to add category:', err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    }
  };

  // Update a category
  const updateCategory = async () => {
    if (!editingCategory) return;

    try {
      const response = await fetch(`/api/categories/${editingCategory._id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`Error updating category: ${response.status}`);
      }

      const data = asApiResponse<Category>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      showSuccessToast('Category updated successfully');
      setEditingCategory(null);
      setFormData({ name: '', description: '', parentCategory: '' });
      fetchCategoriesAndParts(); // Refresh the list
    } catch (err) {
      console.error('Failed to update category:', err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    }
  };

  // Delete a category
  const deleteCategory = async (categoryId: string) => {
    if (!confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
      return;
    }

    try {
      const response = await fetch(`/api/categories/${categoryId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Error deleting category: ${response.status}`);
      }

      const data = asApiResponse<{ message: string }>(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      showSuccessToast('Category deleted successfully');
      fetchCategoriesAndParts(); // Refresh the list
    } catch (err) {
      console.error('Failed to delete category:', err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    }
  };

  // Toggle category expansion
  const toggleExpand = (categoryId: string) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryId]: !prev[categoryId]
    }));

    // Fetch parts if expanding and not already loaded
    if (!expandedCategories[categoryId]) {
      fetchPartsForCategory(categoryId);
    }
  };

  // Filter categories by search query
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (category.description && category.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingCategory) {
      updateCategory();
    } else {
      addCategory();
    }
  };

  // Open edit modal
  const handleEdit = (category: Category, e: React.MouseEvent) => {
    e.stopPropagation();
    setEditingCategory(category);
    setFormData({
      name: category.name,
      description: category.description || '',
      parentCategory: category.parentCategory?._id || ''
    });
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background text-foreground">
      <Header title="Part Categories" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-medium text-gray-900 dark:text-gray-100">Part Categories</h2>

          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search categories..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-64"
              />
            </div>

            <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
              <Button
                variant="secondary"
                className="rounded-full"
                onClick={() => {
                  setEditingCategory(null);
                  setFormData({ name: '', description: '', parentCategory: '' });
                  setShowAddModal(true);
                }}
              >
                <Plus size={16} className="mr-2" />
                <span>Add Category</span>
              </Button>
            </motion.div>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2 text-gray-600 dark:text-gray-400">Loading categories...</span>
          </div>
        ) : error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-red-700 dark:text-red-400">
            <p>{error}</p>
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={fetchCategoriesAndParts}
            >
              Retry
            </Button>
          </div>
        ) : filteredCategories.length === 0 ? (
          <div className="text-center py-12">
            <Package className="h-12 w-12 mx-auto text-gray-400" />
            <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">No categories found</h3>
            <p className="mt-2 text-gray-500 dark:text-gray-400">
              {searchQuery ? 'Try a different search term' : 'Get started by adding your first category'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredCategories.map((category, index) => (
              <motion.div
                key={category._id}
                initial={{ y: 20, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ duration: 0.3, delay: 0.05 * index }}
                className="bg-card rounded-xl shadow-sm dark:shadow-gray-900/20 overflow-hidden"
              >
                <div
                  className="flex justify-between items-center p-4 cursor-pointer hover:bg-muted/50 transition-colors"
                  onClick={() => toggleExpand(category._id)}
                >
                  <div className="flex items-center">
                    <div className="bg-muted p-3 rounded-lg mr-4">
                      <Package size={20} className="text-gray-700 dark:text-gray-300" />
                    </div>
                    <div>
                      <h3 className="text-lg font-medium text-gray-800 dark:text-gray-100">{category.name}</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {category.description || 'No description'}
                        {category.parentCategory && (
                          <span className="ml-2 text-xs bg-muted px-2 py-0.5 rounded-full">
                            Parent: {category.parentCategory.name}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3">
                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="rounded-full h-7 w-7"
                        onClick={(e) => handleEdit(category, e)}
                        aria-label={`Edit category ${category.name}`}
                      >
                        <Edit size={16} />
                      </Button>
                    </motion.div>

                    <motion.div whileHover={{ scale: 1.1 }} whileTap={{ scale: 0.9 }}>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="rounded-full h-7 w-7 text-destructive hover:text-destructive/90"
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteCategory(category._id);
                        }}
                        aria-label={`Delete category ${category.name}`}
                      >
                        <Trash2 size={16} />
                      </Button>
                    </motion.div>

                    <motion.div
                      animate={{ rotate: expandedCategories[category._id] ? 180 : 0 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ChevronDown size={20} className="text-gray-500 dark:text-gray-400" />
                    </motion.div>
                  </div>
                </div>

                {/* Expanded Content - Part List */}
                {expandedCategories[category._id] && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="border-t border-border px-4 py-3"
                  >
                    {loadingParts[category._id] ? (
                      <div className="flex justify-center items-center py-4">
                        <Loader2 className="h-5 w-5 animate-spin text-primary" />
                        <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Loading parts...</span>
                      </div>
                    ) : category.parts && category.parts.length > 0 ? (
                      <div className="mt-1">
                        <h4 className="text-xs font-semibold text-gray-600 dark:text-gray-300 mb-1">Associated Parts:</h4>
                        <ul className="space-y-1 max-h-48 overflow-y-auto text-xs">
                          {category.parts.map(part => (
                            <li key={part._id} className="flex justify-between items-center p-1 hover:bg-muted/50 rounded">
                              <span className="text-gray-700 dark:text-gray-300">{part.name}</span>
                              <span className="text-gray-500 dark:text-gray-400 font-mono text-xs">ID: {part._id}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ) : categoryParts[category._id]?.length ? (
                      <ul className="space-y-1 max-h-60 overflow-y-auto">
                        {categoryParts[category._id]?.map(part => (
                          <li key={part._id} className="flex justify-between items-center text-sm p-1 hover:bg-muted/50 rounded">
                            <span className="text-gray-700 dark:text-gray-300">{part.name}</span>
                            <span className="text-gray-500 dark:text-gray-400 font-mono text-xs">ID: {part._id}</span>
                          </li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-sm text-gray-500 dark:text-gray-400 py-2">No parts associated with this category or failed to load.</p>
                    )}
                  </motion.div>
                )}
              </motion.div>
            ))}
          </div>
        )}

        {/* Add/Edit Category Modal would go here */}
        {/* This would be implemented with a modal component */}
      </div>
    </div>
  );
};

export default Categories;

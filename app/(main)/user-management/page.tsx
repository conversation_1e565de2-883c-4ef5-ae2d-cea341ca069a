"use client";

import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/app/components/data-display/alert";
import { Badge } from "@/app/components/data-display/badge";
import { Button } from "@/app/components/forms/Button";
import { Input } from '@/app/components/forms/Input';
import { Label } from '@/app/components/forms/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Switch } from '@/app/components/forms/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import Header from '@/app/components/layout/Header';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/app/components/navigation/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuTrigger } from '@/app/components/navigation/DropdownMenu';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/app/components/navigation/tabs';
import { useTheme } from '@/app/contexts/ThemeContext';
import { asApiResponse, asUsersResponse, extractApiError, hasApiError } from '@/app/types/api-responses';
import { User } from '@/app/types/user';
import { zodResolver } from '@hookform/resolvers/zod';
import { format } from 'date-fns';
import { motion } from 'framer-motion';
import {
    AlertTriangle,
    Edit,
    Loader2,
    MoreHorizontal,
    RefreshCw,
    Search,
    Trash2,
    UserCog,
    User as UserIcon,
    UserPlus,
    Users
} from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { showNetworkErrorToast, showErrorToast, showSuccessToast } from '@/app/components/feedback';
import * as z from 'zod';

// Define form schema
const userFormSchema = z.object({
  username: z.string().min(3, "Username must be at least 3 characters"),
  email: z.string().email("Invalid email address"),
  fullName: z.string().min(2, "Full name must be at least 2 characters"),
  role: z.enum(['admin', 'manager', 'staff', 'viewer']),
  isActive: z.boolean(),
  password: z.string().min(6, "Password must be at least 6 characters").optional(),
  confirmPassword: z.string().optional()
}).refine(data => {
  // If password is provided, confirmPassword must match
  if (data.password && data.confirmPassword) {
    return data.password === data.confirmPassword;
  }
  return true;
}, {
  message: "Passwords do not match",
  path: ["confirmPassword"]
});

type UserFormValues = z.infer<typeof userFormSchema>;

const UserManagement: React.FC = () => {
  const { theme } = useTheme();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [isLoadingAction, setIsLoadingAction] = useState(false);
  const [currentAction, setCurrentAction] = useState<'add' | 'edit'>('add');
  const [selectedTab, setSelectedTab] = useState<string>('all-users');

  // Form setup with zod validation
  const form = useForm<UserFormValues>({
    resolver: zodResolver(userFormSchema),
    defaultValues: {
      username: '',
      email: '',
      fullName: '',
      role: 'staff',
      isActive: true,
      password: '',
      confirmPassword: ''
    }
  });

  // Fetch users on component mount
  useEffect(() => {
    fetchUsers();
  }, [currentPage, pageSize, roleFilter, statusFilter]);

  // Fetch users from API
  const fetchUsers = async () => {
    setIsLoading(true);
    setError(null);
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString()
      });

      // Add role filter if not 'all'
      if (roleFilter !== 'all') {
        params.append('role', roleFilter);
      }

      // Add status filter if not 'all'
      if (statusFilter !== 'all') {
        params.append('status', statusFilter === 'active' ? 'true' : 'false');
      }

      // Add search query if present
      if (searchQuery) {
        params.append('search', searchQuery);
      }

      const response = await fetch(`/api/users?${params.toString()}`);

      if (!response.ok) {
        throw new Error(`Error fetching users: ${response.status}`);
      }

      const data = asUsersResponse(await response.json());

      if (hasApiError(data)) {
        throw new Error(extractApiError(data) || 'Unknown error occurred');
      }

      setUsers((data.data || []).map((user: any) => ({
        ...user,
        role: user.role as 'admin' | 'manager' | 'staff' | 'viewer'
      })));

      // Update pagination info
      if (data.pagination) {
        setTotalPages(data.pagination.totalPages);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
      showNetworkErrorToast(fetchUsers, { customMessage: 'Failed to load users' });
    } finally {
      setIsLoading(false);
    }
  };

  // Open dialog for adding or editing a user
  const openDialog = (user: User | null = null) => {
    setCurrentAction(user ? 'edit' : 'add');
    setCurrentUser(user);

    if (user) {
      // Set form values for editing
      form.reset({
        username: user.username,
        email: user.email,
        fullName: user.fullName ?? '',
        role: user.role as "admin" | "manager" | "staff" | "viewer",
        isActive: user.isActive,
        password: '',
        confirmPassword: ''
      });
    } else {
      // Reset form for adding
      form.reset({
        username: '',
        email: '',
        fullName: '',
        role: 'staff',
        isActive: true,
        password: '',
        confirmPassword: ''
      });
    }

    setIsDialogOpen(true);
  };

  // Close dialog
  const closeDialog = () => {
    setIsDialogOpen(false);
    setCurrentUser(null);
    form.reset();
  };

  // Open delete confirmation dialog
  const openDeleteDialog = (user: User) => {
    setCurrentUser(user);
    setIsDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const closeDeleteDialog = () => {
    setIsDeleteDialogOpen(false);
    setCurrentUser(null);
  };

  // Handle form submission
  const onSubmit = async (data: UserFormValues) => {
    setIsLoadingAction(true);
    try {
      const method = currentAction === 'edit' ? 'PUT' : 'POST';
      const url = currentAction === 'edit' && currentUser
        ? `/api/users/${currentUser.username}`
        : '/api/users';

      // Remove confirmPassword before sending to API
      const { confirmPassword, ...userData } = data;

      // If password is empty and editing, remove it from the request
      if (currentAction === 'edit' && !userData.password) {
        delete userData.password;
      }

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(userData)
      });

      if (!response.ok) {
        throw new Error(`Error ${currentAction === 'edit' ? 'updating' : 'creating'} user: ${response.status}`);
      }

      const responseData = asApiResponse<User>(await response.json());

      if (hasApiError(responseData)) {
        throw new Error(extractApiError(responseData) || 'Unknown error occurred');
      }

      showSuccessToast(`User ${currentAction === 'edit' ? 'updated' : 'created'} successfully`);
      closeDialog();
      fetchUsers();
    } catch (err) {
      console.error(`Error ${currentAction === 'edit' ? 'updating' : 'creating'} user:`, err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Delete a user
  const deleteUser = async () => {
    if (!currentUser) return;

    setIsLoadingAction(true);
    try {
      const response = await fetch(`/api/users/${currentUser.username}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error(`Error deleting user: ${response.status}`);
      }

      const data = await response.json() as { error?: string };

      if (data.error) {
        throw new Error(data.error);
      }

      showSuccessToast('User deleted successfully');
      closeDeleteDialog();
      fetchUsers();
    } catch (err) {
      console.error('Error deleting user:', err);
      showErrorToast({ error: err instanceof Error ? err.message : String(err) });
    } finally {
      setIsLoadingAction(false);
    }
  };

  // Handle search
  const handleSearch = () => {
    setCurrentPage(1); // Reset to first page when searching
    fetchUsers();
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Get role badge
  const getRoleBadge = (role: string = 'staff') => {
    switch (role) {
      case 'admin':
        return <Badge variant="destructive">Admin</Badge>;
      case 'manager':
        return <Badge variant="default">Manager</Badge>;
      case 'staff':
        return <Badge variant="secondary">Staff</Badge>;
      case 'viewer':
        return <Badge variant="outline">Viewer</Badge>;
      default:
        return <Badge variant="outline">{role}</Badge>;
    }
  };

  // Get status badge
  const getStatusBadge = (isActive: boolean = true) => {
    return isActive
      ? <Badge variant="secondary">Active</Badge>
      : <Badge variant="destructive">Inactive</Badge>;
  };

  return (
    <div className="flex-1 overflow-y-auto bg-background">
      <Header title="User Management" />

      <div className="px-8 pb-8">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center">
            <UserCog className="text-foreground mr-2" size={24} />
            <h1 className="text-2xl font-semibold text-foreground">User Management</h1>
          </div>

          <Button
            onClick={() => openDialog()}
            className="bg-primary hover:bg-primary/90"
          >
            <UserPlus size={16} className="mr-2" />
            New User
          </Button>
        </div>

        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              {error}
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={fetchUsers}
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Tabs */}
        <Tabs
          value={selectedTab}
          onValueChange={setSelectedTab}
          className="mb-6"
        >
          <TabsList>
            <TabsTrigger value="all-users">All Users</TabsTrigger>
            <TabsTrigger value="admins">Administrators</TabsTrigger>
            <TabsTrigger value="staff">Staff</TabsTrigger>
            <TabsTrigger value="inactive">Inactive Users</TabsTrigger>
          </TabsList>

          <TabsContent value="all-users">
            {/* Search and Filter Controls */}
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1 flex gap-2">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    type="text"
                    placeholder="Search users..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <Button variant="outline" onClick={handleSearch}>
                  Search
                </Button>
              </div>

              <div className="flex gap-2">
                <Select
                  value={roleFilter}
                  onValueChange={(value) => {
                    setRoleFilter(value);
                    setCurrentPage(1); // Reset to first page when changing filter
                  }}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="manager">Manager</SelectItem>
                    <SelectItem value="staff">Staff</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={statusFilter}
                  onValueChange={(value) => {
                    setStatusFilter(value);
                    setCurrentPage(1); // Reset to first page when changing filter
                  }}
                >
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>

                <Select
                  value={pageSize.toString()}
                  onValueChange={(value) => {
                    setPageSize(parseInt(value));
                    setCurrentPage(1); // Reset to first page when changing page size
                  }}
                >
                  <SelectTrigger className="w-[100px]">
                    <SelectValue placeholder="Page size" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="5">5</SelectItem>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Users Table */}
            <div className="bg-card rounded-lg shadow-md dark:shadow-gray-900/30 overflow-hidden">
              {isLoading ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                  <span className="ml-2 text-gray-600 dark:text-gray-400">Loading users...</span>
                </div>
              ) : users.length === 0 ? (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 mx-auto text-gray-400" />
                  <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-gray-100">No users found</h3>
                  <p className="mt-2 text-gray-500 dark:text-gray-400">
                    {searchQuery || roleFilter !== 'all' || statusFilter !== 'all'
                      ? 'Try changing your search or filter criteria'
                      : 'Get started by creating your first user'}
                  </p>
                  {(searchQuery || roleFilter !== 'all' || statusFilter !== 'all') && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => {
                        setSearchQuery('');
                        setRoleFilter('all');
                        setStatusFilter('all');
                        fetchUsers();
                      }}
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Reset Filters
                    </Button>
                  )}
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b border-border bg-muted/50">
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">User</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Email</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Role</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Created</th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Actions</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-border">
                      {users.map((user, index) => (
                        <motion.tr
                          key={user._id}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: index * 0.05 }}
                          className="hover:bg-muted/50"
                        >
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                <UserIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                              </div>
                              <div className="ml-4">
                                <div className="text-sm font-medium text-foreground">{user.fullName}</div>
                                <div className="text-xs text-gray-500 dark:text-gray-400">@{user.username}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            <div className="text-sm text-foreground">{user.email}</div>
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {getRoleBadge(user.role)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap">
                            {getStatusBadge(user.isActive)}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-sm text-foreground">
                            {format(new Date(user.createdAt), 'MMM dd, yyyy')}
                          </td>
                          <td className="px-4 py-3 whitespace-nowrap text-right">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="icon">
                                  <MoreHorizontal className="h-4 w-4" />
                                  <span className="sr-only">Open menu</span>
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => openDialog(user)}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  Edit User
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => openDeleteDialog(user)}
                                  className="text-red-600 dark:text-red-400"
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete User
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </td>
                        </motion.tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}

              {/* Pagination */}
              {!isLoading && users.length > 0 && (
                <div className="flex items-center justify-between px-4 py-3 border-t border-border">
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Showing <span className="font-medium">{users.length}</span> of{' '}
                    <span className="font-medium">{totalPages * pageSize}</span> results
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                        <Button
                          key={page}
                          variant={currentPage === page ? 'default' : 'outline'}
                          size="sm"
                          className="w-8 h-8 p-0"
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </Button>
                      ))}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="admins">
            <Card>
              <CardHeader>
                <CardTitle>Administrators</CardTitle>
                <CardDescription>Manage users with administrator privileges</CardDescription>
              </CardHeader>
              <CardContent>
                <p>This tab will show only administrator users.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="staff">
            <Card>
              <CardHeader>
                <CardTitle>Staff Members</CardTitle>
                <CardDescription>Manage regular staff accounts</CardDescription>
              </CardHeader>
              <CardContent>
                <p>This tab will show only staff users.</p>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="inactive">
            <Card>
              <CardHeader>
                <CardTitle>Inactive Users</CardTitle>
                <CardDescription>Manage deactivated user accounts</CardDescription>
              </CardHeader>
              <CardContent>
                <p>This tab will show only inactive users.</p>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>

      {/* Add/Edit User Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>{currentAction === 'add' ? 'Add New User' : 'Edit User'}</DialogTitle>
            <DialogDescription>
              {currentAction === 'add'
                ? 'Create a new user account with appropriate permissions.'
                : 'Update user details and permissions.'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="username" className="text-right">
                  Username
                </Label>
                <div className="col-span-3">
                  <Input
                    id="username"
                    {...form.register('username')}
                    disabled={currentAction === 'edit'}
                    className={form.formState.errors.username ? 'border-red-500' : ''}
                  />
                  {form.formState.errors.username && (
                    <p className="text-red-500 text-xs mt-1">{form.formState.errors.username.message}</p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="fullName" className="text-right">
                  Full Name
                </Label>
                <div className="col-span-3">
                  <Input
                    id="fullName"
                    {...form.register('fullName')}
                    className={form.formState.errors.fullName ? 'border-red-500' : ''}
                  />
                  {form.formState.errors.fullName && (
                    <p className="text-red-500 text-xs mt-1">{form.formState.errors.fullName.message}</p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="email" className="text-right">
                  Email
                </Label>
                <div className="col-span-3">
                  <Input
                    id="email"
                    type="email"
                    {...form.register('email')}
                    className={form.formState.errors.email ? 'border-red-500' : ''}
                  />
                  {form.formState.errors.email && (
                    <p className="text-red-500 text-xs mt-1">{form.formState.errors.email.message}</p>
                  )}
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="role" className="text-right">
                  Role
                </Label>
                <div className="col-span-3">
                  <Select
                    value={form.watch('role')}
                    onValueChange={(value) => form.setValue('role', value as any)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Administrator</SelectItem>
                      <SelectItem value="manager">Manager</SelectItem>
                      <SelectItem value="staff">Staff</SelectItem>
                      <SelectItem value="viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="isActive" className="text-right">
                  Status
                </Label>
                <div className="col-span-3 flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={form.watch('isActive')}
                    onCheckedChange={(checked) => form.setValue('isActive', checked)}
                  />
                  <Label htmlFor="isActive" className="cursor-pointer">
                    {form.watch('isActive') ? 'Active' : 'Inactive'}
                  </Label>
                </div>
              </div>
              {currentAction === 'add' && (
                <>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="password" className="text-right">
                      Password
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="password"
                        type="password"
                        {...form.register('password')}
                        className={form.formState.errors.password ? 'border-red-500' : ''}
                      />
                      {form.formState.errors.password && (
                        <p className="text-red-500 text-xs mt-1">{form.formState.errors.password.message}</p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="confirmPassword" className="text-right">
                      Confirm
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="confirmPassword"
                        type="password"
                        {...form.register('confirmPassword')}
                        className={form.formState.errors.confirmPassword ? 'border-red-500' : ''}
                      />
                      {form.formState.errors.confirmPassword && (
                        <p className="text-red-500 text-xs mt-1">{form.formState.errors.confirmPassword.message}</p>
                      )}
                    </div>
                  </div>
                </>
              )}
              {currentAction === 'edit' && (
                <>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="password" className="text-right">
                      New Password
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="password"
                        type="password"
                        placeholder="Leave blank to keep current"
                        {...form.register('password')}
                        className={form.formState.errors.password ? 'border-red-500' : ''}
                      />
                      {form.formState.errors.password && (
                        <p className="text-red-500 text-xs mt-1">{form.formState.errors.password.message}</p>
                      )}
                    </div>
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="confirmPassword" className="text-right">
                      Confirm
                    </Label>
                    <div className="col-span-3">
                      <Input
                        id="confirmPassword"
                        type="password"
                        placeholder="Leave blank to keep current"
                        {...form.register('confirmPassword')}
                        className={form.formState.errors.confirmPassword ? 'border-red-500' : ''}
                      />
                      {form.formState.errors.confirmPassword && (
                        <p className="text-red-500 text-xs mt-1">{form.formState.errors.confirmPassword.message}</p>
                      )}
                    </div>
                  </div>
                </>
              )}
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={closeDialog}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoadingAction}>
                {isLoadingAction ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {currentAction === 'add' ? 'Creating...' : 'Updating...'}
                  </>
                ) : (
                  <>
                    {currentAction === 'add' ? 'Create User' : 'Update User'}
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete User</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this user? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {currentUser && (
              <div className="flex items-center p-4 border rounded-md">
                <div className="flex-shrink-0 h-10 w-10 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                  <UserIcon className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                </div>
                <div className="ml-4">
                  <div className="text-sm font-medium text-foreground">{currentUser.fullName}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">@{currentUser.username}</div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">{currentUser.email}</div>
                </div>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={closeDeleteDialog}>
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={deleteUser}
              disabled={isLoadingAction}
            >
              {isLoadingAction ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  Delete User
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default UserManagement;
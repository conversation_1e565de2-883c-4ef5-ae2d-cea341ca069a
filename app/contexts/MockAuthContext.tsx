"use client";

import React, { createContext, useContext, ReactNode } from 'react';
import { UserRole } from '@/app/types';

// Define user type to match original AuthContext
interface User {
  id: string;
  email: string;
  role: UserRole;
  firstName?: string;
  lastName?: string;
  avatarUrl?: string;
}

// Define auth context type to match original
interface AuthContextType {
  user: User | null;
  session: any | null;
  isLoading: boolean;
  error: string | null;
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
  hasRole: (requiredRoles: UserRole | UserRole[]) => boolean;
}

// Mock user with admin access to everything
const mockUser: User = {
  id: 'mock-user-id',
  email: '<EMAIL>',
  role: 'admin',
  firstName: 'Admin',
  lastName: 'User'
};

// Create context with default values
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component that always provides the mock user
export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Mock hasRole function that always returns true
  const hasRole = (requiredRoles: UserRole | UserRole[]) => true;

  // Mock signIn function that always succeeds
  const signIn = async () => ({ success: true });

  // Mock signOut function that does nothing
  const signOut = async () => {};

  // Provide context value with mock user and functions
  const value: AuthContextType = {
    user: mockUser,
    session: { user: mockUser },
    isLoading: false,
    error: null,
    signIn,
    signOut,
    isAuthenticated: true,
    hasRole
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Export hook that always returns the mock auth data
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
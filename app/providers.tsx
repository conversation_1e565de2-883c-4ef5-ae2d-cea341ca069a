"use client";

import React from 'react';
import { ThemeProvider } from "@/app/contexts/ThemeContext";
import { AuthProvider } from "@/app/contexts/MockAuthContext";
import { AppProvider } from "@/app/contexts/AppContext";
import { Toaster } from 'react-hot-toast';

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider>
      <AuthProvider>
        <AppProvider>
          {children}
          <Toaster position="top-right" /> 
        </AppProvider>
      </AuthProvider>
    </ThemeProvider>
  );
} 
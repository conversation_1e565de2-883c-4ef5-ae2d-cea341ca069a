'use client';

import { useTheme } from '@/app/contexts/ThemeContext';
import { cn } from '@/app/lib/utils';
import React, { useEffect, useRef } from 'react';

interface EnhancedBackgroundProps {
  children: React.ReactNode;
  patternType?: 'grid' | 'dots' | 'warp' | 'interactive-grid';
  interactiveMode?: boolean;
  className?: string;
}

/**
 * Enhanced background component with interactive patterns
 * Uses Magic UI effects for creating engaging backgrounds
 */
export function EnhancedBackground({
  children,
  patternType = 'interactive-grid',
  interactiveMode = true,
  className
}: EnhancedBackgroundProps) {
  const { theme } = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Interactive grid effect
  useEffect(() => {
    if (!interactiveMode || patternType !== 'interactive-grid' || !containerRef.current) return;
    
    const container = containerRef.current;
    
    const handleMouseMove = (e: MouseEvent) => {
      if (!container) return;
      
      const rect = container.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      
      // Update CSS variables for the interactive effect
      container.style.setProperty('--x', `${x}px`);
      container.style.setProperty('--y', `${y}px`);
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, [interactiveMode, patternType]);
  
  // Render different background patterns based on type
  const renderBackgroundPattern = () => {
    switch (patternType) {
      case 'grid':
        return (
          <div className="absolute inset-0 overflow-hidden">
            <div className={cn(
              "absolute inset-0 opacity-[0.03]",
              theme.isDark ? 'bg-grid-white/10' : 'bg-grid-black/10'
            )} style={{ backgroundSize: '40px 40px' }} />
          </div>
        );
        
      case 'dots':
        return (
          <div className="absolute inset-0 overflow-hidden">
            <div className={cn(
              "absolute inset-0 opacity-[0.03]",
              theme.isDark ? 'bg-dot-white/10' : 'bg-dot-black/10'
            )} style={{ backgroundSize: '20px 20px' }} />
          </div>
        );
        
      case 'warp':
        return (
          <div className="absolute inset-0 overflow-hidden bg-background">
            <div className="absolute inset-0 bg-gradient-to-br from-indigo-500/5 via-purple-500/5 to-pink-500/5" />
            <div className="absolute inset-0 backdrop-blur-[100px]" />
          </div>
        );
        
      case 'interactive-grid':
      default:
        return (
          <div className="absolute inset-0 overflow-hidden">
            <div className={cn(
              "absolute inset-0 opacity-[0.03]",
              theme.isDark ? 'bg-grid-white/10' : 'bg-grid-black/10'
            )} style={{ backgroundSize: '40px 40px' }} />
            
            {/* Interactive highlight effect */}
            <div className="absolute inset-0 bg-[radial-gradient(circle_800px_at_var(--x,_100px)_var(--y,_100px),rgba(99,102,241,0.15),transparent_80%)] opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100" />
          </div>
        );
    }
  };
  
  return (
    <div 
      ref={containerRef}
      className={cn(
        "relative w-full min-h-screen group",
        className
      )}
    >
      {renderBackgroundPattern()}
      <div className="relative z-10">{children}</div>
    </div>
  );
}
"use client";

import { useTheme } from '@/app/contexts/ThemeContext';
import { motion } from 'framer-motion';
import React from 'react';
import { ActionCardProps, colorClasses } from './types';

/**
 * Client component for ActionCard with animations and interactivity
 */
const ActionCardClient: React.FC<ActionCardProps> = ({
  icon,
  label,
  onClick,
  color = 'blue',
  className = '',
}) => {
  const { theme } = useTheme();

  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3 }
    },
    hover: {
      y: -5,
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
      transition: { duration: 0.2 }
    }
  };

  return (
    <motion.button
      onClick={onClick}
      whileHover="hover"
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      className={`flex items-center justify-center space-x-2 p-4 rounded-lg shadow transition-colors bg-card/80 backdrop-blur-md shadow-md hover:bg-muted/50 ${className}`}
    >
      {icon && <div className={colorClasses[color]}>{icon}</div>}
      <span className="text-sm font-medium">{label}</span>
    </motion.button>
  );
};

export default ActionCardClient; 
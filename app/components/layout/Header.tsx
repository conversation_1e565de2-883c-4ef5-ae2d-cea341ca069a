"use client";

/**
 * Header Component
 * Displays the page title and control elements at the top of the page
 * Includes responsive behavior for mobile and desktop views
 */
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu } from 'lucide-react';
import { useAppContext } from '@/app/contexts/AppContext';
import { useTheme } from '@/app/contexts/ThemeContext';
import HeaderRightControls from '@/app/components/layout/HeaderRightControls';

/**
 * Props for the Header component
 */
interface HeaderProps {
  /** Page title to display */
  title?: string;
  /** Optional children to render on the right side before HeaderRightControls */
  children?: React.ReactNode;
  /** Optional role attribute for accessibility */
  role?: string;
  /** Optional aria-labelledby attribute for accessibility */
  'aria-labelledby'?: string;
}

/**
 * Header component for navigation and page identification
 * Features a responsive design with mobile sidebar toggle and scroll effects
 */
const Header: React.FC<HeaderProps> = ({ title = "Dashboard", children, role, 'aria-labelledby': ariaLabelledBy }) => {
  const { sidebarExpanded, setSidebarExpanded } = useAppContext();
  const { theme } = useTheme();
  const [isMobile, setIsMobile] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  /**
   * Check window size and update mobile state accordingly
   */
  useEffect(() => {
    const checkWindowSize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkWindowSize();

    // Add event listener
    window.addEventListener('resize', checkWindowSize);

    // Cleanup
    return () => window.removeEventListener('resize', checkWindowSize);
  }, []);

  /**
   * Handle scroll effects to update header background
   */
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  /**
   * Toggle the sidebar expanded state
   */
  const toggleSidebar = () => {
    setSidebarExpanded(!sidebarExpanded);
  };

  return (
    <motion.div
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      data-framer-motion="header-container"
      role={role}
      aria-labelledby={ariaLabelledBy}
      className={`sticky top-0 z-40 flex justify-between items-center py-5 px-5 md:px-8 backdrop-blur-lg ${
        scrolled
          ? 'bg-background/80 border-b border-border'
          : 'bg-transparent'
      } transition-all duration-300`}
    >
      <div className="flex items-center">
        {isMobile && (
          <motion.div
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={toggleSidebar}
            data-framer-motion="mobile-menu-button"
            className="mr-3 p-2 rounded-full hover:bg-muted/80 cursor-pointer backdrop-blur-sm transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ring"
            role="button"
            tabIndex={0}
            aria-label={`${sidebarExpanded ? 'Close' : 'Open'} navigation menu`}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                toggleSidebar();
              }
            }}
          >
            <Menu size={24} className="text-muted-foreground" />
          </motion.div>
        )}
        <motion.h1
          initial={{ x: -20, opacity: 0 }}
          animate={{ x: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          data-framer-motion="header-title"
          className="text-2xl md:text-4xl font-light truncate text-foreground"
        >
          {title}
        </motion.h1>
      </div>

      <motion.div
        className="flex items-center"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        data-framer-motion="header-controls"
      >
        {children}
        <HeaderRightControls />
      </motion.div>
    </motion.div>
  );
};

export default Header;
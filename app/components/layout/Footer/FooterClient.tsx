"use client";

import { useTheme } from '@/app/contexts/ThemeContext';
import { motion } from 'framer-motion';
import { ExternalLink } from 'lucide-react';
import Link from 'next/link';
import React from 'react';
import { FooterProps } from './types';

/**
 * Client component for the Footer
 * Includes animations and interactive elements
 */
const FooterClient: React.FC<FooterProps> = ({
  companyName = 'Trend Innovations',
  copyrightYear = new Date().getFullYear(),
  links = [
    { label: 'Privacy Policy', href: '/privacy' },
    { label: 'Terms of Service', href: '/terms' },
    { label: 'Contact Us', href: '/contact' }
  ],
  className = ''
}) => {
  const { theme } = useTheme();

  return (
    <motion.footer
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`mt-auto py-6 px-4 border-t border-border bg-card text-muted-foreground ${className}`}
    >
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center">
        <div className="mb-4 md:mb-0">
          <p className="text-sm">
            © {copyrightYear} {companyName}. All rights reserved.
          </p>
        </div>
        
        <div className="flex flex-wrap gap-6">
          {links.map((link) => (
            <motion.div
              key={link.href}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {link.external ? (
                <a 
                  href={link.href} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className={`text-sm flex items-center gap-1 ${
                    theme.isDark
                      ? 'hover:text-white'
                      : 'hover:text-gray-800'
                  }`}
                >
                  {link.label}
                  <ExternalLink size={12} />
                </a>
              ) : (
                <Link
                  href={link.href}
                  className={`text-sm ${
                    theme.isDark
                      ? 'hover:text-white'
                      : 'hover:text-gray-800'
                  }`}
                >
                  {link.label}
                </Link>
              )}
            </motion.div>
          ))}
        </div>
      </div>
    </motion.footer>
  );
};

export default FooterClient; 
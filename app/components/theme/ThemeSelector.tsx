"use client";

import { Button } from '@/app/components/forms/Button';
import { getThemeConfig } from '@/app/config/themes.config';
import { useTheme } from '@/app/contexts/ThemeContext';
import { ThemeMode, ThemeVariant } from '@/app/types/theme.types';
import { AnimatePresence, motion } from 'framer-motion';
import { Moon, Palette, RotateCcw, Sun } from 'lucide-react';
import React, { useState } from 'react';
import { ThemePreviewCard } from './ThemePreviewCard';

/**
 * Theme Selector Component
 * Provides a comprehensive interface for selecting theme variants and modes
 */
export const ThemeSelector: React.FC = () => {
  const { 
    currentTheme, 
    availableThemes, 
    setMode, 
    setVariant, 
    setTheme 
  } = useTheme();
  
  const [selectedMode, setSelectedMode] = useState<ThemeMode>(currentTheme.mode);

  /**
   * Handle theme variant selection
   */
  const handleVariantSelect = (variant: ThemeVariant) => {
    setTheme(variant, selectedMode);
  };

  /**
   * Handle mode toggle
   */
  const handleModeChange = (mode: ThemeMode) => {
    setSelectedMode(mode);
    setTheme(currentTheme.variant, mode);
  };

  /**
   * Reset to default theme
   */
  const handleReset = () => {
    setTheme('default', 'light');
    setSelectedMode('light');
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-foreground">
            Choose Your Theme
          </h3>
          <p className="text-sm text-muted-foreground">
            Select a theme variant and mode to customize your experience
          </p>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleReset}
          className="flex items-center space-x-2"
        >
          <RotateCcw size={14} />
          <span>Reset</span>
        </Button>
      </div>

      {/* Mode Selection */}
      <div className="space-y-3">
        <h4 className="text-sm font-medium text-foreground">
          Theme Mode
        </h4>
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
          <motion.button
            className={`flex items-center justify-center space-x-2 rounded-lg border-2 px-4 py-3 transition-all flex-1 sm:flex-none ${
              selectedMode === 'light'
                ? 'border-primary bg-primary/10 text-primary'
                : 'border-border bg-card text-foreground hover:border-primary/50'
            }`}
            onClick={() => handleModeChange('light')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Sun size={18} />
            <span className="font-medium">Light</span>
          </motion.button>

          <motion.button
            className={`flex items-center justify-center space-x-2 rounded-lg border-2 px-4 py-3 transition-all flex-1 sm:flex-none ${
              selectedMode === 'dark'
                ? 'border-primary bg-primary/10 text-primary'
                : 'border-border bg-card text-foreground hover:border-primary/50'
            }`}
            onClick={() => handleModeChange('dark')}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Moon size={18} />
            <span className="font-medium">Dark</span>
          </motion.button>
        </div>
      </div>

      {/* Theme Variants */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Palette size={16} className="text-primary" />
            <h4 className="text-sm font-medium text-foreground">
              Theme Variants
            </h4>
          </div>
          <div className="text-xs text-muted-foreground">
            {availableThemes.length} themes available
          </div>
        </div>

        {/* Professional Themes Section */}
        <div className="space-y-3">
          <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
            Professional Themes
          </h5>
          <AnimatePresence mode="wait">
            <motion.div
              key={`professional-${selectedMode}`}
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3, staggerChildren: 0.05 }}
            >
              {availableThemes
                .filter((theme: any) => ['default', 'blue', 'github', 'linear', 'vercel', 'enterprise', 'navy', 'modern'].includes(theme.variant))
                .map((themeVariant: any) => {
                  const themeConfig = getThemeConfig(themeVariant.variant, selectedMode as 'light' | 'dark');
                  const isSelected =
                    currentTheme.variant === themeVariant.variant &&
                    currentTheme.mode === selectedMode;

                  // Skip if theme config is not available
                  if (!themeConfig) {
                    return null;
                  }

                  return (
                    <motion.div
                      key={`${themeVariant.variant}-${selectedMode}`}
                      initial={{ opacity: 0, scale: 0.9 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.2 }}
                    >
                      <ThemePreviewCard
                        theme={themeConfig}
                        isSelected={isSelected}
                        onSelect={() => handleVariantSelect(themeVariant.variant)}
                      />
                    </motion.div>
                  );
                })}
            </motion.div>
          </AnimatePresence>
        </div>

        {/* Additional Themes Section (if any) */}
        {availableThemes.filter((theme: any) => !['default', 'blue', 'github', 'linear', 'vercel', 'enterprise', 'navy', 'modern'].includes(theme.variant)).length > 0 && (
          <div className="space-y-3">
            <h5 className="text-xs font-medium text-muted-foreground uppercase tracking-wider">
              Additional Themes
            </h5>
            <AnimatePresence mode="wait">
              <motion.div
                key={`additional-${selectedMode}`}
                className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, staggerChildren: 0.05 }}
              >
                {availableThemes
                  .filter((theme: any) => !['default', 'blue', 'github', 'linear', 'vercel', 'enterprise', 'navy', 'modern'].includes(theme.variant))
                  .map((themeVariant: any) => {
                    const themeConfig = getThemeConfig(themeVariant.variant, selectedMode as 'light' | 'dark');
                    const isSelected =
                      currentTheme.variant === themeVariant.variant &&
                      currentTheme.mode === selectedMode;

                    // Skip if theme config is not available
                    if (!themeConfig) {
                      return null;
                    }

                    return (
                      <motion.div
                        key={`${themeVariant.variant}-${selectedMode}`}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                      >
                        <ThemePreviewCard
                          theme={themeConfig}
                          isSelected={isSelected}
                          onSelect={() => handleVariantSelect(themeVariant.variant)}
                        />
                      </motion.div>
                    );
                  })}
              </motion.div>
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Current Selection Info */}
      <div className="rounded-lg border border-border bg-muted/50 p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-foreground">
              Current Theme
            </p>
            <p className="text-xs text-muted-foreground">
              {currentTheme.config.name} • {currentTheme.mode} mode
            </p>
          </div>
          <div className="flex space-x-1">
            <div
              className="h-4 w-4 rounded border border-border/50"
              style={{ backgroundColor: currentTheme.config.preview.primary }}
              title="Primary color"
            />
            <div
              className="h-4 w-4 rounded border border-border/50"
              style={{ backgroundColor: currentTheme.config.preview.secondary }}
              title="Secondary color"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThemeSelector;


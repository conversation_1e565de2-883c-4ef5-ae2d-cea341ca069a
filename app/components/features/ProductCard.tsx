"use client";

import { useTheme } from '@/app/contexts/ThemeContext';
import { motion } from 'framer-motion';
import { AlertTriangle, ArrowDown, ArrowUp, Plus, Tag } from 'lucide-react';
import React from 'react';

/**
 * Product data structure for the ProductCard component
 */
interface ProductCardProduct {
  /** Unique identifier for the product */
  id: string;
  /** Name of the product */
  name: string;
  /** Optional description of the product */
  description?: string | null | undefined;
  /** Optional URL to the product image */
  imageUrl?: string | null | undefined;
  /** Current stock quantity */
  currentStock: number;
  /** Minimum stock level before reordering */
  reorderLevel: number;
  /** Optional product category */
  category?: string | null | undefined;
  /** Optional product price */
  price?: number;
  /** Optional total value of on-hand inventory */
  onHandValue?: number | null | undefined;
  /** Optional demand level classification */
  demand?: 'High' | 'Medium' | 'Low' | null;
}

/**
 * Props for the ProductCard component
 */
interface ProductCardProps {
  /** Product data to display */
  product: ProductCardProduct;
  /** Whether this is a placeholder card */
  isPlaceholder?: boolean;
  /** Whether this card should be featured/highlighted */
  isFeatured?: boolean;
}

/**
 * Card component for displaying product information
 * Shows product details, stock status, and pricing information
 */
const ProductCard: React.FC<ProductCardProps> = ({ product, isPlaceholder = false, isFeatured = false }) => {
  const { theme } = useTheme();

  /**
   * Determines the stock status based on current stock and reorder level
   * @returns Object containing color, label, and icon for the stock status
   */
  const getStockStatus = () => {
    if (product.currentStock === 0) return { color: 'red', label: 'Out of Stock', icon: <AlertTriangle size={14} /> };
    if (product.currentStock < product.reorderLevel) return { color: 'yellow', label: 'Low Stock', icon: <AlertTriangle size={14} /> };
    return { color: 'green', label: 'In Stock', icon: <ArrowUp size={14} /> };
  };

  const getDemandLabel = () => {
    if (!product.demand) return null;

    switch (product.demand) {
      case 'High':
        return { color: 'red', label: 'High Demand', icon: <ArrowUp size={14} /> };
      case 'Medium':
        return { color: 'yellow', label: 'Medium Demand', icon: <ArrowUp size={14} /> };
      case 'Low':
        return { color: 'blue', label: 'Low Demand', icon: <ArrowDown size={14} /> };
      default:
        return null;
    }
  };

  const stockStatus = getStockStatus();
  const demandLabel = getDemandLabel();

  // CSS classes for status colors using semantic theme classes
  const getStatusClasses = (type: string, color: string) => {
    if (type === 'bg') {
      switch (color) {
        case 'red': return 'bg-destructive/10';
        case 'yellow': return 'bg-warning/10';
        case 'green': return 'bg-success/10';
        case 'blue': return 'bg-info/10';
        default: return 'bg-muted';
      }
    }
    // For text colors
    switch (color) {
      case 'red': return 'text-destructive';
      case 'yellow': return 'text-warning';
      case 'green': return 'text-success';
      case 'blue': return 'text-info';
      default: return 'text-muted-foreground';
    }
  };

  return (
    <motion.div
      whileHover={{ y: -2, scale: 1.02 }}
      className="bg-card backdrop-blur-md shadow-sm hover:shadow-md rounded-xl overflow-hidden h-full cursor-pointer border border-border hover:border-primary/50 transition-all duration-300 ease-in-out"
    >
      {/* Product Image */}
      <div className="relative w-full h-40 bg-muted">
        {product.imageUrl ? (
          <img
            src={product.imageUrl}
            alt={product.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            {isPlaceholder ? (
              <div className="text-center">
                <Plus size={24} className="mx-auto mb-2 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Add New Product</span>
              </div>
            ) : (
              <Tag size={24} className="text-muted-foreground" />
            )}
          </div>
        )}

        {/* Stock Status Tag */}
        {!isPlaceholder && stockStatus && (
          <div className={`absolute top-2 right-2 px-2 py-1 text-xs rounded-full ${getStatusClasses('bg', stockStatus.color)} ${getStatusClasses('text', stockStatus.color)} flex items-center`}>
            {stockStatus.icon}
            <span className="ml-1">{stockStatus.label}</span>
          </div>
        )}
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="font-medium text-foreground truncate">{product.name}</h3>

        {product.description && (
          <p className="text-sm text-muted-foreground mt-1 h-10 overflow-hidden">
            {product.description}
          </p>
        )}

        <div className="mt-3 flex items-center justify-between">
          <div className="flex items-center">
            {product.category && (
              <span className="text-xs bg-muted text-muted-foreground px-2 py-1 rounded">
                {product.category}
              </span>
            )}
          </div>

          {demandLabel && !isPlaceholder && (
            <span className={`text-xs ${getStatusClasses('bg', demandLabel.color)} ${getStatusClasses('text', demandLabel.color)} px-2 py-1 rounded-full flex items-center`}>
              {demandLabel.icon}
              <span className="ml-1">{demandLabel.label}</span>
            </span>
          )}
        </div>

        {!isPlaceholder && (
          <div className="mt-4 pt-3 border-t border-border">
            <div className="flex justify-between items-center">
              <div>
                <div className="text-xs text-muted-foreground">Stock</div>
                <div className="font-medium">{product.currentStock} units</div>
              </div>

              {product.onHandValue !== undefined && product.onHandValue !== null && (
                <div className="text-right">
                  <div className="text-xs text-muted-foreground">Value</div>
                  <div className="font-medium">${product.onHandValue.toLocaleString()}</div>
                </div>
              )}
            </div>
          </div>
        )}

        {isPlaceholder && (
          <div className="mt-4 pt-3 border-t border-border">
            <div className="flex justify-center">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-3 py-1.5 bg-primary hover:bg-primary-foreground text-primary-foreground text-sm rounded flex items-center"
              >
                <Plus size={14} className="mr-1" />
                <span>Add New Product</span>
              </motion.button>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  );
};

export default ProductCard;
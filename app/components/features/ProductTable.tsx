"use client";

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { confirmAlert } from 'react-confirm-alert';
import 'react-confirm-alert/src/react-confirm-alert.css';
import { Product } from '@/app/types';
import { useTheme } from '@/app/contexts/ThemeContext';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import {
  createFeatureProductColumns,
  FeatureProductColumnData,
  FeatureProductTableActions
} from '@/app/components/data-display/data-table/column-definitions';
import { PackageOpen } from 'lucide-react';

/**
 * Props for the ProductTable component
 */
interface ProductTableProps {
  /** List of products to display in the table */
  products: Product[];
  /** Function called when a product is edited */
  onEdit?: (product: Product) => void;
  /** Alias for onEdit for backward compatibility */
  onEditProduct?: (product: Product) => void;
  /** Function called when a product is deleted */
  onDelete?: (id: string) => void;
  /** Alias for onDelete for backward compatibility */
  onDeleteProduct?: (id: string) => void;
  /** Current page number for pagination */
  currentPage?: number;
  /** Total number of pages for pagination */
  totalPages?: number;
  /** Function called when page is changed */
  onPageChange?: (pageNumber: number) => void;
}

/**
 * Table component for displaying and managing products
 * Supports sorting, searching, and row actions (edit, delete, view)
 */
const ProductTable: React.FC<ProductTableProps> = ({
  products,
  onEdit,
  onEditProduct,
  onDelete,
  onDeleteProduct,
  currentPage,
  totalPages,
  onPageChange
}) => {
  const { theme } = useTheme();
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  // Transform products data for DataTable
  const tableData: FeatureProductColumnData[] = useMemo(() => {
    return products.map(product => ({
      ...product,
      id: product.id,
      name: product.name,
      supplierManufacturer: product.supplierManufacturer || '',
      description: product.description || '',
      currentStock: product.inventory?.currentStock ?? product.currentStock ?? 0,
      reorderLevel: product.reorderLevel || 0,
    }));
  }, [products]);

  // Handle delete confirmation
  const handleDeleteClick = (id: string) => {
    confirmAlert({
      title: 'Confirm Delete',
      message: 'Are you sure you want to delete this part?',
      buttons: [
        {
          label: 'Yes',
          onClick: () => {
            if (onDelete) onDelete(id);
            if (onDeleteProduct) onDeleteProduct(id);
          }
        },
        {
          label: 'No',
          onClick: () => {}
        }
      ]
    });
  };

  // Handle select/deselect all
  const toggleSelectAll = () => {
    if (selectedProducts.length === tableData.length && tableData.length > 0) {
      setSelectedProducts([]);
    } else {
      setSelectedProducts(tableData.map((p) => p.id));
    }
  };

  // Handle select/deselect a single product
  const toggleSelectProduct = (id: string) => {
    if (selectedProducts.includes(id)) {
      setSelectedProducts(selectedProducts.filter((pid) => pid !== id));
    } else {
      setSelectedProducts([...selectedProducts, id]);
    }
  };

  // Define actions for the DataTable
  const actions: FeatureProductTableActions = useMemo(() => {
    const actionObj: FeatureProductTableActions = {};
    if (onEdit) {
      actionObj.onEdit = (product: FeatureProductColumnData) => {
        // Convert FeatureProductColumnData back to Product for the callback
        onEdit(product as any);
      };
    }
    if (onEditProduct) {
      actionObj.onEditProduct = (product: FeatureProductColumnData) => {
        // Convert FeatureProductColumnData back to Product for the callback
        onEditProduct(product as any);
      };
    }
    if (onDelete || onDeleteProduct) {
      actionObj.onDelete = handleDeleteClick;
      actionObj.onDeleteProduct = handleDeleteClick;
    }
    return actionObj;
  }, [onEdit, onEditProduct, onDelete, onDeleteProduct]);

  // Create columns with actions and selection handlers
  const columns = useMemo(() =>
    createFeatureProductColumns(actions, selectedProducts, toggleSelectProduct, toggleSelectAll),
    [actions, selectedProducts]
  );

  // Empty state component
  if (products.length === 0) {
    return (
      <motion.div
        initial={{ y: 20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden mt-6 shadow-md dark:shadow-gray-900/30 p-8 text-center"
      >
        <PackageOpen className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium mb-2">No Products Found</h3>
        <p className="text-sm text-muted-foreground">
          No parts found matching your search criteria.
        </p>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      className="bg-white dark:bg-gray-800 rounded-3xl overflow-hidden mt-6 shadow-md dark:shadow-gray-900/30"
    >
      <div className="p-4">
        <StandardizedTable
          data={tableData}
          columns={columns}
          searchPlaceholder="Search products..."
          enableSearch={true}
          enableViewToggle={false}
          // FIXED: Updated from legacy tableProps pattern to direct props pattern
          enableSorting={true}
          enableFiltering={true}
          enableGlobalSearch={false} // Using StandardizedTable's search instead
          enablePagination={true}
          enableRowSelection={true}
          enableColumnVisibility={false}
          mobileDisplayMode="cards"
          density="normal"
          initialPagination={{ pageIndex: 0, pageSize: 10 }}
          pageSizeOptions={[10, 20, 50, 100]}
          caption={`Feature products table with ${products.length} items`}
        />
      </div>

    </motion.div>
  );
};

export default ProductTable;
'use client';

import { Badge } from '@/app/components/data-display/badge';
import { <PERSON><PERSON>ip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/app/components/feedback/tooltip';
import { Product } from '@/app/components/tables/ProductsTable/types';
import { useTheme } from '@/app/contexts/ThemeContext';
import { cn } from '@/app/lib/utils';
import { AlertCircle, CheckCircle2, Wrench, XCircle } from 'lucide-react';

interface ProductStatusBadgeProps {
  product: Product;
  showLabel?: boolean;
  size?: 'sm' | 'default' | 'lg';
  className?: string;
}

/**
 * Component to display product status with appropriate visual indicators
 */
export function ProductStatusBadge({
  product,
  showLabel = true,
  size = 'default',
  className,
}: ProductStatusBadgeProps) {
  const { theme } = useTheme();

  // Determine status based on product properties
  const getStatus = () => {
    switch (product.status) {
      case 'active':
        return {
          label: 'Active',
          variant: 'success' as const,
          icon: <CheckCircle2 className={cn(
            size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            'text-theme-success'
          )} />,
          description: 'This product is active and available for sale',
        };
      case 'discontinued':
        return {
          label: 'Discontinued',
          variant: 'destructive' as const,
          icon: <XCircle className={cn(
            size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            'text-theme-error'
          )} />,
          description: 'This product has been discontinued and is no longer available',
        };
      case 'in_development':
        return {
          label: 'In Development',
          variant: 'secondary' as const,
          icon: <Wrench className={cn(
            size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            'text-theme-info'
          )} />,
          description: 'This product is currently in development',
        };
      default:
        return {
          label: 'Unknown',
          variant: 'outline' as const,
          icon: <AlertCircle className={cn(
            size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4',
            'text-theme-secondary'
          )} />,
          description: 'Product status is unknown',
        };
    }
  };

  const status = getStatus();

  // Get variant-specific styles using theme variables
  const getVariantStyles = () => {
    switch (status.variant) {
      case 'success':
        return "border-success/30 bg-success/10 text-success";
      case 'destructive':
        return "border-destructive/30 bg-destructive/10 text-destructive";
      case 'secondary':
        return "border-info/30 bg-info/10 text-info";
      case 'outline':
      default:
        return "border-border bg-muted/50 text-muted-foreground";
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge
            variant="outline"
            className={cn(
              'flex items-center gap-1 font-normal',
              getVariantStyles(),
              size === 'sm' ? 'text-xs py-0 px-2' : size === 'lg' ? 'text-sm py-1 px-3' : 'text-xs py-0.5 px-2.5',
              className
            )}
          >
            {status.icon}
            {showLabel && <span>{status.label}</span>}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          {status.description}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

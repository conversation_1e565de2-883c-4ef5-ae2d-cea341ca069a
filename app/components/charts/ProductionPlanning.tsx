"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, Clock, Users, ArrowRight, AlertTriangle } from 'lucide-react';
import { WorkOrder, Product } from '@/app/types';
import { useTheme } from '@/app/contexts/ThemeContext';

/**
 * Props for the ProductionPlanning component
 */
interface ProductionPlanningProps {
  /** List of work orders to display */
  workOrders: WorkOrder[];
  /** List of products to check for material shortages */
  products: Product[];
}

/**
 * Component that displays production planning information including resource utilization,
 * upcoming deadlines, and material shortages
 */
const ProductionPlanning: React.FC<ProductionPlanningProps> = ({ workOrders, products }) => {
  const { theme } = useTheme();

  /**
   * Resource utilization percentages for different resource types
   * In a real implementation, this would be calculated from actual data
   */
  const resourceUtilization = {
    machinery: 75, // 75% utilization
    labor: 60,     // 60% utilization
    materials: 85  // 85% utilization
  };

  /**
   * Overall production efficiency percentage
   * In a real implementation, this would be calculated from actual data
   */
  const productionEfficiency = 82; // 82% efficiency

  /**
   * Calculate upcoming deadlines from work orders
   * Filters out completed and cancelled work orders
   * Sorts by end date (earliest first)
   * Takes the top 3 upcoming deadlines
   */
  const upcomingDeadlines = (workOrders || [])
    .filter(wo => wo.status !== 'completed' && wo.status !== 'cancelled' && wo.endDate)
    .sort((a, b) => new Date(a.endDate!).getTime() - new Date(b.endDate!).getTime())
    .slice(0, 3);

  /**
   * Calculate material shortages from products
   * Identifies products where current stock is below reorder level
   * Takes the top 3 shortages
   */
  const materialShortages = (products || [])
    .filter(p => (p.currentStock || 0) < (p.reorderLevel || 0))
    .slice(0, 3);

  return (
    <div className="p-4 h-full flex flex-col">
      <div className="flex justify-between items-start mb-3">
        <h3 className="text-lg font-medium text-foreground">Production Planning</h3>
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          className="w-6 h-6 bg-muted rounded-full flex items-center justify-center cursor-pointer"
        >
          <ArrowRight size={14} className="text-muted-foreground" />
        </motion.div>
      </div>

      <div className="flex-1 overflow-hidden flex flex-col space-y-3">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 flex-1 min-h-0">
          {/* Resource Utilization */}
          <div className="bg-muted/50 rounded-lg p-3 overflow-hidden">
            <h4 className="font-medium text-foreground mb-2 flex items-center text-sm">
              <Users size={16} className="mr-2 text-blue-600 dark:text-blue-400" />
              Resource Utilization
            </h4>
            <div className="space-y-2">
              {Object.entries(resourceUtilization).map(([resource, value]) => (
                <div key={resource} className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span className="capitalize text-muted-foreground">{resource}</span>
                    <span className="font-medium text-foreground">{value}%</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full ${
                        value > 90 ? 'bg-red-500 dark:bg-red-600' :
                        value > 70 ? 'bg-green-500 dark:bg-green-600' :
                        'bg-blue-500 dark:bg-blue-600'
                      }`}
                      style={{ width: `${value}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
            <div className="mt-3 pt-2 border-t border-border">
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">Overall Efficiency</span>
                <span className="text-sm font-medium text-green-600 dark:text-green-400">{productionEfficiency}%</span>
              </div>
            </div>
          </div>

          {/* Upcoming Deadlines */}
          <div className="bg-muted/50 rounded-lg p-3 overflow-hidden">
            <h4 className="font-medium text-foreground mb-2 flex items-center text-sm">
              <Calendar size={16} className="mr-2 text-blue-600 dark:text-blue-400" />
              Upcoming Deadlines
            </h4>
            {upcomingDeadlines.length > 0 ? (
              <div className="space-y-2 overflow-y-auto max-h-24">
                {upcomingDeadlines.map((wo) => (
                  <div key={wo.id} className="flex items-start">
                    <div className={`w-1.5 h-1.5 mt-1 rounded-full ${
                      wo.priority === 'urgent' ? 'bg-red-500' :
                      wo.priority === 'high' ? 'bg-orange-500' :
                      wo.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                    } mr-2`}></div>
                    <div className="flex-1">
                      <p className="text-xs font-medium text-foreground truncate">{wo.description}</p>
                      <div className="flex items-center text-xs text-muted-foreground">
                        <Clock size={10} className="mr-1" />
                        {new Date(wo.endDate!).toLocaleDateString()}
                      </div>
                    </div>
                    <div className={`px-1.5 py-0.5 text-xs rounded-full capitalize ${
                      wo.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400' :
                      wo.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400' :
                      wo.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400' :
                      'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400'
                    }`}>
                      {wo.priority || 'standard'}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-xs text-muted-foreground">No upcoming deadlines</p>
            )}
          </div>
        </div>

        {/* Material Shortages */}
        <div className="bg-muted/50 rounded-lg p-3 overflow-hidden">
          <h4 className="font-medium text-foreground mb-2 flex items-center text-sm">
            <AlertTriangle size={16} className="mr-2 text-orange-500 dark:text-orange-400" />
            Material Shortages
          </h4>
          {materialShortages.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2 overflow-y-auto max-h-28">
              {materialShortages.map((product) => (
                <div key={product.id} className="bg-background p-2 rounded-lg border border-orange-200 dark:border-orange-900/30">
                  <p className="text-xs font-medium text-foreground truncate">{product.name}</p>
                  <div className="mt-1 flex justify-between">
                    <span className="text-xs text-muted-foreground">Current Stock</span>
                    <span className="text-xs font-medium text-red-600 dark:text-red-400">{product.currentStock}</span>
                  </div>
                  <div className="mt-0.5 flex justify-between">
                    <span className="text-xs text-muted-foreground">Reorder Level</span>
                    <span className="text-xs font-medium text-muted-foreground">{product.reorderLevel}</span>
                  </div>
                  <div className="mt-1.5">
                    <button className="w-full text-xs bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 py-1 rounded hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-colors">
                      Create Purchase Order
                    </button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-xs text-muted-foreground">No material shortages detected</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProductionPlanning;
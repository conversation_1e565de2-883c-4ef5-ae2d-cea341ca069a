/**
 * Test file for DataTable component
 * This file contains basic tests to verify the DataTable functionality
 */

import { DataTableColumn } from './index';
import { StandardizedTable } from '../../tables/StandardizedTable';

// Sample test data
interface TestItem {
  id: string;
  name: string;
  status: string;
  count: number;
}

const testData: TestItem[] = [
  { id: '1', name: 'Item 1', status: 'active', count: 10 },
  { id: '2', name: 'Item 2', status: 'inactive', count: 5 },
  { id: '3', name: 'Item 3', status: 'active', count: 15 },
];

const testColumns: DataTableColumn<TestItem>[] = [
  {
    id: 'name',
    header: 'Name',
    accessorKey: 'name',
    mobilePriority: 1,
  },
  {
    id: 'status',
    header: 'Status',
    accessorKey: 'status',
    mobilePriority: 2,
  },
  {
    id: 'count',
    header: 'Count',
    accessorKey: 'count',
    hideOnMobile: true,
  },
];

/**
 * Basic DataTable test component
 */
export function DataTableTest() {
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">DataTable Test</h2>
      <StandardizedTable
        data={testData}
        columns={testColumns}
        enableSorting={true}
        enableFiltering={true}
        enableGlobalSearch={true}
        enablePagination={true}
        caption="Test data table"
        searchPlaceholder="Search test data..."
        enableSearch={true}
        enableViewToggle={false}
      />
    </div>
  );
}

/**
 * Test function to verify DataTable props
 */
export function testDataTableProps() {
  const requiredProps = {
    data: testData,
    columns: testColumns,
  };

  const optionalProps = {
    enableSorting: true,
    enableFiltering: true,
    enableGlobalSearch: true,
    enablePagination: true,
    enableRowSelection: false,
    enableColumnVisibility: true,
    mobileDisplayMode: 'cards' as const,
    density: 'normal' as const,
    isLoading: false,
    showCaption: true,
    stickyHeader: false,
  };

  console.log('DataTable required props:', requiredProps);
  console.log('DataTable optional props:', optionalProps);
  
  return { requiredProps, optionalProps };
}

/**
 * Test mobile responsiveness
 */
export function testMobileResponsiveness() {
  const mobileColumns = testColumns.filter(col => !col.hideOnMobile);
  const prioritizedColumns = mobileColumns.sort(
    (a, b) => (a.mobilePriority || 999) - (b.mobilePriority || 999)
  );

  console.log('Mobile columns:', mobileColumns);
  console.log('Prioritized columns:', prioritizedColumns);
  
  return { mobileColumns, prioritizedColumns };
}

/**
 * Test accessibility features
 */
export function testAccessibilityFeatures() {
  const accessibilityFeatures = {
    tableCaption: 'Test data table',
    columnHeaders: testColumns.map(col => col.header),
    keyboardNavigation: true,
    screenReaderSupport: true,
    ariaLabels: true,
  };

  console.log('Accessibility features:', accessibilityFeatures);
  
  return accessibilityFeatures;
}

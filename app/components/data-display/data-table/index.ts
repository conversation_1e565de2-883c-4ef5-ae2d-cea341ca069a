/**
 * DataTable component exports
 * Modern, responsive table component using TanStack Table
 */

export { DataTableToolbar } from './DataTableToolbar';
export { MobileCardView } from './MobileCardView';
export { DataTableSkeleton } from './DataTableSkeleton';
export { DataTableError } from './DataTableError';
export { DataTableEmpty } from './DataTableEmpty';

export type {
  DataTableColumn,
  DataTableToolbarProps,
  MobileCardProps,
  MobileDisplayMode,
  TableDensity,
} from './types';

// Column definitions and utilities
export {
  // Products table columns
  createProductsSimpleColumns,
  createProductsComplexColumns,
  type ProductColumnData,
  type ProductsTableActions,

  // Assemblies table columns
  createAssembliesColumns,
  type AssemblyColumnData,
  type AssembliesTableActions,

  // Inventory table columns
  createInventoryColumns,
  type InventoryColumnData,
  type InventoryTableActions,

  // Feature product table columns
  createFeatureProductColumns,
  type FeatureProductColumnData,
  type FeatureProductTableActions,

  // Work orders table columns
  createWorkOrdersSimpleColumns,
  createWorkOrdersComplexColumns,
  type WorkOrderColumnData,
  type WorkOrdersTableActions,

  // Inventory transactions table columns
  createInventoryTransactionsSimpleColumns,
  createInventoryTransactionsComplexColumns,
  type InventoryTransactionColumnData,
  type InventoryTransactionsTableActions,

  // Purchase orders table columns
  createPurchaseOrdersComplexColumns,
  type PurchaseOrderColumnData,
  type PurchaseOrdersTableActions,
} from './column-definitions';

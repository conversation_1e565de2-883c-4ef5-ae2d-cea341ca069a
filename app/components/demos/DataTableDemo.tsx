"use client";

import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { DataTableColumn } from '@/app/components/data-display/data-table';
import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/app/components/navigation/DropdownMenu';
import { AlertCircle, Edit, Eye, MoreHorizontal, Trash2 } from 'lucide-react';
import { useMemo } from 'react';

// Sample inventory data structure based on the existing InventoryTable
interface InventoryItem {
  _id: string;
  partNumber: string;
  name: string;
  description: string;
  status: string;
  unitOfMeasure: string;
  cost: number;
  reorderLevel?: number;
  inventory?: {
    currentStock: number;
    safetyStockLevel: number;
    maximumStockLevel: number;
    abcClassification: string;
  };
  supplier?: {
    _id: string;
    name: string;
  };
  category?: {
    _id: string;
    name: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Sample data for testing
const sampleInventoryData: InventoryItem[] = [
  {
    _id: '1',
    partNumber: 'P001',
    name: 'Resistor 10K Ohm',
    description: 'Carbon film resistor, 1/4W, 5% tolerance',
    status: 'active',
    unitOfMeasure: 'pcs',
    cost: 0.05,
    reorderLevel: 100,
    inventory: {
      currentStock: 250,
      safetyStockLevel: 100,
      maximumStockLevel: 500,
      abcClassification: 'A',
    },
    supplier: {
      _id: 's1',
      name: 'Electronic Components Inc.',
    },
    category: {
      _id: 'c1',
      name: 'Resistors',
    },
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
  },
  {
    _id: '2',
    partNumber: 'P002',
    name: 'Capacitor 100uF',
    description: 'Electrolytic capacitor, 25V, radial',
    status: 'active',
    unitOfMeasure: 'pcs',
    cost: 0.15,
    reorderLevel: 50,
    inventory: {
      currentStock: 25,
      safetyStockLevel: 50,
      maximumStockLevel: 200,
      abcClassification: 'B',
    },
    supplier: {
      _id: 's2',
      name: 'Capacitor Solutions Ltd.',
    },
    category: {
      _id: 'c2',
      name: 'Capacitors',
    },
    createdAt: '2024-01-02T00:00:00Z',
    updatedAt: '2024-01-16T00:00:00Z',
  },
  {
    _id: '3',
    partNumber: 'P003',
    name: 'LED Red 5mm',
    description: 'High brightness red LED, 5mm package',
    status: 'active',
    unitOfMeasure: 'pcs',
    cost: 0.25,
    reorderLevel: 200,
    inventory: {
      currentStock: 0,
      safetyStockLevel: 200,
      maximumStockLevel: 1000,
      abcClassification: 'A',
    },
    supplier: {
      _id: 's3',
      name: 'LED World',
    },
    category: {
      _id: 'c3',
      name: 'LEDs',
    },
    createdAt: '2024-01-03T00:00:00Z',
    updatedAt: '2024-01-17T00:00:00Z',
  },
];

interface DataTableDemoProps {
  onView?: (item: InventoryItem) => void;
  onEdit?: (item: InventoryItem) => void;
  onDelete?: (item: InventoryItem) => void;
}

export function DataTableDemo({
  onView,
  onEdit,
  onDelete,
}: DataTableDemoProps) {
  
  // Stock status badge component
  const getStockStatusBadge = (item: InventoryItem) => {
    const stock = item.inventory?.currentStock ?? 0;
    const reorderLevel = item.reorderLevel ?? 0;

    if (stock <= 0) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Out of Stock
        </Badge>
      );
    }

    if (reorderLevel > 0 && stock <= reorderLevel) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <AlertCircle className="h-3 w-3" />
          Low Stock
        </Badge>
      );
    }

    return (
      <Badge variant="default" className="bg-success/10 text-success">
        In Stock
      </Badge>
    );
  };

  // Row actions component
  const renderRowActions = (item: InventoryItem) => (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onView?.(item)}>
          <Eye className="mr-2 h-4 w-4" />
          View
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => onEdit?.(item)}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuItem 
          onClick={() => onDelete?.(item)}
          className="text-destructive"
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );

  // Column definitions
  const columns: DataTableColumn<InventoryItem>[] = useMemo(() => [
    {
      id: 'partNumber',
      header: 'Part Number',
      accessorKey: 'partNumber',
      mobilePriority: 1,
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          {row.original.partNumber}
        </div>
      ),
    },
    {
      id: 'name',
      header: 'Name',
      accessorKey: 'name',
      mobilePriority: 2,
      cell: ({ row }) => (
        <div>
          <div className="font-medium">{row.original.name}</div>
          <div className="text-sm text-muted-foreground md:hidden">
            {row.original.description}
          </div>
        </div>
      ),
    },
    {
      id: 'description',
      header: 'Description',
      accessorKey: 'description',
      hideOnMobile: true,
      cell: ({ row }) => (
        <div className="max-w-[200px] truncate">
          {row.original.description}
        </div>
      ),
    },
    {
      id: 'currentStock',
      header: 'Stock',
      accessorFn: (row) => row.inventory?.currentStock ?? 0,
      mobilePriority: 3,
      cell: ({ row }) => {
        const stock = row.original.inventory?.currentStock ?? 0;
        return (
          <div className="text-right font-mono">
            {stock.toLocaleString()} {row.original.unitOfMeasure}
          </div>
        );
      },
    },
    {
      id: 'status',
      header: 'Status',
      accessorKey: 'status',
      mobilePriority: 4,
      cell: ({ row }) => getStockStatusBadge(row.original),
      mobileRender: (value, row) => getStockStatusBadge(row),
    },
    {
      id: 'cost',
      header: 'Unit Cost',
      accessorKey: 'cost',
      hideOnMobile: true,
      cell: ({ row }) => (
        <div className="text-right font-mono">
          ${row.original.cost.toFixed(2)}
        </div>
      ),
    },
    {
      id: 'supplier',
      header: 'Supplier',
      accessorFn: (row) => row.supplier?.name ?? 'N/A',
      hideOnMobile: true,
      cell: ({ row }) => (
        <div className="max-w-[150px] truncate">
          {row.original.supplier?.name ?? 'N/A'}
        </div>
      ),
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: ({ row }) => renderRowActions(row.original),
      enableSorting: false,
      enableHiding: false,
    },
  ], [onView, onEdit, onDelete]);

  return (
    <div className="space-y-4">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Inventory Management</h2>
        <p className="text-muted-foreground">
          Manage your inventory with the new responsive DataTable component.
        </p>
      </div>

      <StandardizedTable
        data={sampleInventoryData}
        columns={columns}
        searchPlaceholder="Search inventory..."
        enableSearch={true}
        enableViewToggle={false}
        renderActions={() => (
          <Button size="sm">
            Add New Item
          </Button>
        )}
        // FIXED: Updated from legacy tableProps pattern to direct props pattern
        enableSorting={true}
        enableFiltering={true}
        enableGlobalSearch={false}
        enablePagination={true}
        enableColumnVisibility={false}
        mobileDisplayMode="cards"
        density="normal"
        caption="Inventory items with stock levels and supplier information"
        onRowClick={(item) => console.log('Row clicked:', item)}
      />
    </div>
  );
}

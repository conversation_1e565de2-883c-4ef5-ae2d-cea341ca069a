'use client';

import React, { useMemo } from 'react';
import { format } from 'date-fns';
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import {
  createInventoryTransactionsComplexColumns,
  type InventoryTransactionColumnData,
  type InventoryTransactionsTableActions
} from '@/app/components/data-display/data-table';

interface InventoryTransactionsTableClientProps {
  transactions: any[];
  isLoading?: boolean;
  error?: string | null;
  actions?: InventoryTransactionsTableActions;
}

// Helper function to calculate previous stock value from event-sourced transaction data
function calculatePreviousStock(transaction: any, quantity: number): number | string | null {
  // For legacy transactions, use the existing field
  if (transaction.previousStock !== null && transaction.previousStock !== undefined) {
    return transaction.previousStock;
  }

  // For event-sourced transactions, show the movement source instead of absolute stock
  if (transaction.from && transaction.from.type) {
    return `From: ${transaction.from.type}`;
  }

  // Fallback for transactions without clear movement data
  return 'N/A';
}

// Helper function to calculate new stock value from event-sourced transaction data
function calculateNewStock(transaction: any, quantity: number): number | string | null {
  // For legacy transactions, use the existing field
  if (transaction.newStock !== null && transaction.newStock !== undefined) {
    return transaction.newStock;
  }

  // For event-sourced transactions, show the movement destination instead of absolute stock
  if (transaction.to && transaction.to.type) {
    return `To: ${transaction.to.type}`;
  }

  // Fallback for transactions without clear movement data
  return 'N/A';
}

// Transform raw transaction data to table format
function transformTransactionData(transaction: any): InventoryTransactionColumnData {
  // Extract item information - prioritize pre-extracted fields from main page transformation
  let itemName = 'Unknown Part';
  let itemId = '';
  let partNumber = '';

  // First check if main page has already extracted the data
  if (transaction.partName) {
    itemName = transaction.partName;
  }
  if (transaction.partNumber) {
    partNumber = transaction.partNumber;
  }

  // Fallback: check if partId is a populated object (direct API response)
  if (!itemName && typeof transaction.partId === 'object' && transaction.partId) {
    if (transaction.partId.name) {
      itemName = transaction.partId.name;
    } else if (transaction.partId.partNumber) {
      itemName = transaction.partId.partNumber;
    } else if (transaction.partId.description) {
      itemName = transaction.partId.description;
    }
  }

  // Fallback: extract partNumber from populated object
  if (!partNumber && typeof transaction.partId === 'object' && transaction.partId) {
    if (transaction.partId.partNumber) {
      partNumber = transaction.partId.partNumber;
    }
  }

  // Special case: if partId is a string that looks like a part number (not an ObjectId)
  if (!itemName && typeof transaction.partId === 'string' && transaction.partId) {
    // If it's not a valid ObjectId, it's likely a part number
    if (!transaction.partId.match(/^[0-9a-fA-F]{24}$/)) {
      itemName = `Part ${transaction.partId}`;
      partNumber = transaction.partId;
    }
  }

  // Final fallback to legacy field names
  if (!itemName && transaction.itemName) {
    itemName = transaction.itemName;
  }



  // Extract item ID (MongoDB ObjectID for compatibility)
  if (typeof transaction.partId === 'object' && transaction.partId) {
    itemId = transaction.partId._id;
  } else if (typeof transaction.partId === 'string') {
    itemId = transaction.partId;
  } else if (typeof transaction.itemId === 'string') {
    itemId = transaction.itemId;
  } else if (typeof transaction.itemId === 'object' && transaction.itemId) {
    itemId = transaction.itemId._id;
  }

  // Extract warehouse information - prioritize pre-extracted fields from main page transformation
  let warehouseName = 'Unknown';
  let warehouseId = '';
  let warehouseLocationId = '';

  // First check if main page has already extracted the data
  if (transaction.warehouseName) {
    warehouseName = transaction.warehouseName;
  }
  if (transaction.warehouseLocationId) {
    warehouseLocationId = transaction.warehouseLocationId;
  }

  // Fallback: check if warehouseId is a populated object (direct API response)
  if (!warehouseName && typeof transaction.warehouseId === 'object' && transaction.warehouseId) {
    warehouseName = transaction.warehouseId.name || transaction.warehouseId.location_id || 'Unknown';
  }

  // Fallback: extract location_id from populated object
  if (!warehouseLocationId && typeof transaction.warehouseId === 'object' && transaction.warehouseId) {
    if (transaction.warehouseId.location_id) {
      warehouseLocationId = transaction.warehouseId.location_id;
    }
  }

  // NEW: Try to get warehouse info from from/to objects if available
  if (!warehouseName || warehouseName === 'Unknown') {
    // Check 'to' warehouse first (for receipts)
    if (transaction.to?.warehouse) {
      warehouseName = transaction.to.warehouse.name || transaction.to.warehouse.location_id || 'Unknown';
      warehouseLocationId = transaction.to.warehouse.location_id || warehouseLocationId;
      warehouseId = transaction.to.warehouseId || warehouseId;
    }
    // Then check 'from' warehouse (for shipments)
    else if (transaction.from?.warehouse) {
      warehouseName = transaction.from.warehouse.name || transaction.from.warehouse.location_id || 'Unknown';
      warehouseLocationId = transaction.from.warehouse.location_id || warehouseLocationId;
      warehouseId = transaction.from.warehouseId || warehouseId;
    }
  }

  // Extract warehouse ID (MongoDB ObjectID for compatibility)
  if (!warehouseId) {
    if (typeof transaction.warehouseId === 'object' && transaction.warehouseId) {
      warehouseId = transaction.warehouseId._id;
    } else if (typeof transaction.warehouseId === 'string') {
      warehouseId = transaction.warehouseId;
    }
  }

  // Extract user information - prioritize pre-extracted fields from main page transformation
  let userName = 'Unknown User';
  let userId = '';

  // Check if userId is a string (unpopulated ObjectId) - user doesn't exist in database
  if (typeof transaction.userId === 'string' && transaction.userId.match(/^[0-9a-fA-F]{24}$/)) {
    userName = 'Deleted User';
  }
  // Check if main page has already extracted valid user data (not "Unknown User")
  else if (transaction.userName && transaction.userName !== 'Unknown User') {
    userName = transaction.userName;
  }
  // Fallback: check if userId is a populated object (direct API response)
  else if (typeof transaction.userId === 'object' && transaction.userId) {
    // Build full name from first_name and last_name, or fallback to username
    const firstName = transaction.userId.first_name || '';
    const lastName = transaction.userId.last_name || '';
    const fullName = `${firstName} ${lastName}`.trim();
    userName = fullName || transaction.userId.username || 'Unknown User';
  }
  // Handle null or invalid userId
  else if (!transaction.userId || transaction.userId === null) {
    userName = 'System User';
  }
  // Final fallback for any other case
  else {
    userName = 'Unknown User';
  }

  // Extract user ID
  if (typeof transaction.userId === 'object' && transaction.userId) {
    userId = transaction.userId._id;
  } else if (typeof transaction.userId === 'string') {
    userId = transaction.userId;
  } else {
    userId = ''; // Handle null case
  }

  // Handle new event-sourced transaction model
  // In the new schema, quantity is always positive and represents amount moved
  const quantity = transaction.quantity || 0;

  // Extract movement information from from/to objects with location support
  let fromLocation = '';
  let toLocation = '';
  let fromLocationName = '';
  let toLocationName = '';
  let fromWarehouseName = '';
  let toWarehouseName = '';
  let movementDescription = '';

  // Helper function to get warehouse and location names
  const getWarehouseAndLocationNames = async (warehouseId: string, locationId?: string) => {
    // This would ideally be cached or pre-fetched, but for now we'll use the IDs
    // In a real implementation, you'd want to resolve these names from the API
    return {
      warehouseName: warehouseId, // Fallback to ID if name not available
      locationName: locationId || null
    };
  };

  if (transaction.from && transaction.to) {
    // Internal transfer between locations/stock types
    const fromStockType = transaction.from.stockType || 'unknown';
    const toStockType = transaction.to.stockType || 'unknown';

    // Extract warehouse and location information
    if (transaction.from.locationId) {
      fromLocationName = transaction.from.locationId; // Will be resolved to name in real implementation
      fromWarehouseName = transaction.from.warehouseId; // Will be resolved to name
      fromLocation = `${fromWarehouseName} - ${fromLocationName} (${fromStockType})`;
    } else {
      fromWarehouseName = transaction.from.warehouseId;
      fromLocation = `${fromWarehouseName} (${fromStockType})`;
    }

    if (transaction.to.locationId) {
      toLocationName = transaction.to.locationId; // Will be resolved to name in real implementation
      toWarehouseName = transaction.to.warehouseId; // Will be resolved to name
      toLocation = `${toWarehouseName} - ${toLocationName} (${toStockType})`;
    } else {
      toWarehouseName = transaction.to.warehouseId;
      toLocation = `${toWarehouseName} (${toStockType})`;
    }

    movementDescription = `${fromLocation} → ${toLocation}`;
  } else if (transaction.from && !transaction.to) {
    // External shipment (sales, scrap)
    const fromStockType = transaction.from.stockType || 'unknown';

    if (transaction.from.locationId) {
      fromLocationName = transaction.from.locationId;
      fromWarehouseName = transaction.from.warehouseId;
      fromLocation = `${fromWarehouseName} - ${fromLocationName} (${fromStockType})`;
    } else {
      fromWarehouseName = transaction.from.warehouseId;
      fromLocation = `${fromWarehouseName} (${fromStockType})`;
    }

    toLocation = 'External';
    movementDescription = `${fromLocation} → External`;
  } else if (!transaction.from && transaction.to) {
    // External receipt (purchase)
    const toStockType = transaction.to.stockType || 'unknown';

    if (transaction.to.locationId) {
      toLocationName = transaction.to.locationId;
      toWarehouseName = transaction.to.warehouseId;
      toLocation = `${toWarehouseName} - ${toLocationName} (${toStockType})`;
    } else {
      toWarehouseName = transaction.to.warehouseId;
      toLocation = `${toWarehouseName} (${toStockType})`;
    }

    fromLocation = 'External';
    movementDescription = `External → ${toLocation}`;
  } else {
    // Fallback for legacy data or adjustments
    movementDescription = 'Stock Adjustment';
  }

  // Handle transaction type field variations and format for display
  const rawTransactionType = transaction.transactionType || transaction.type || 'adjustment_cycle_count';
  const transactionType = formatTransactionType(rawTransactionType);

  // Handle reference field variations
  const referenceType = transaction.referenceType || transaction.referenceModel;
  const referenceNumber = transaction.referenceNumber || transaction.referenceId;

  // Format reference display - handle undefined referenceType
  const reference = referenceNumber
    ? (referenceType && referenceType !== 'undefined' ? `${referenceType}: ${referenceNumber}` : referenceNumber)
    : (referenceType && referenceType !== 'undefined' ? referenceType : '-');

  // Create movement display for the table
  const movementDisplay = movementDescription || `${warehouseName} (${transactionType})`;

  const result = {
    _id: transaction._id,
    transactionId: transaction.transactionId, // Human-readable transaction ID
    transactionType,
    itemName,
    itemId,
    partNumber, // Human-readable part number
    itemType: transaction.itemType || 'Part',
    warehouseName,
    warehouseId,
    warehouseLocationId, // Human-readable warehouse location ID
    quantity: quantity, // Use the new quantity field (always positive)
    previousStock: calculatePreviousStock(transaction, quantity), // Calculate from event-sourced data
    newStock: calculateNewStock(transaction, quantity), // Calculate from event-sourced data
    transactionDate: transaction.transactionDate,
    referenceNumber,
    referenceType,
    reference,
    userName,
    userId,
    notes: transaction.notes || '-',
    // UPDATED: Enhanced location-based movement fields
    fromLocation,
    toLocation,
    fromLocationName,
    toLocationName,
    fromWarehouseName,
    toWarehouseName,
    movementDisplay,
    from: transaction.from,
    to: transaction.to,
  };

  return result;
}

// Helper function to format transaction types for display
function formatTransactionType(type: string): string {
  const typeMap: { [key: string]: string } = {
    // Legacy transaction types
    'stock_in_production': 'Stock In Production',
    'stock_out_production': 'Stock Out Production',
    'stock_in_purchase': 'Stock In Purchase',
    'stock_out_sale': 'Stock Out Sale',
    'adjustment_cycle_count': 'Adjustment Cycle Count',
    'adjustment_manual': 'Adjustment Manual',
    'transfer_in': 'Transfer In',
    'transfer_out': 'Transfer Out',
    'return_from_production': 'Return From Production',
    'return_to_supplier': 'Return To Supplier',
    'sales_shipment': 'Sales Shipment',

    // New event-sourced transaction types
    'purchase_receipt': 'Purchase Receipt',
    'internal_transfer': 'Internal Transfer',
    'scrap_disposal': 'Scrap Disposal',
    'process_move': 'Process Move',
    'adjustment': 'Stock Adjustment'
  };

  return typeMap[type] || type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

export default function InventoryTransactionsTableClient({
  transactions,
  isLoading = false,
  error = null,
  actions = {}
}: InventoryTransactionsTableClientProps) {
  // Transform data for DataTable
  const tableData: InventoryTransactionColumnData[] = useMemo(() => {
    const transformed = transactions.map(transformTransactionData);
    console.log('=== FINAL TABLE DATA ===');
    console.log('First transformed item:', transformed[0]);
    console.log('First item itemName:', transformed[0]?.itemName);
    console.log('First item warehouseName:', transformed[0]?.warehouseName);
    console.log('First item userName:', transformed[0]?.userName);
    console.log('First item quantity:', transformed[0]?.quantity);
    console.log('First item previousStock:', transformed[0]?.previousStock);
    console.log('First item newStock:', transformed[0]?.newStock);
    return transformed;
  }, [transactions]);

  // Create columns with actions
  const columns = useMemo(() => {
    return createInventoryTransactionsComplexColumns(actions);
  }, [actions]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 dark:text-red-400 mb-2">Error loading transactions</p>
          <p className="text-sm text-gray-500">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <StandardizedTable
      data={tableData}
      columns={columns}
      searchPlaceholder="Search transactions..."
      enableSearch={true}
      enableViewToggle={false}
      // FIXED: Updated from legacy tableProps pattern to direct props pattern
      isLoading={isLoading}
      enableGlobalSearch={false}
      enablePagination={true}
      enableSorting={true}
      enableFiltering={true}
      enableColumnVisibility={false}
      mobileDisplayMode="cards"
      density="normal"
      initialPagination={{ pageIndex: 0, pageSize: 50 }}
      pageSizeOptions={[10, 20, 50, 100, 150]}
      onRowClick={(transaction: unknown) => {
        // Open transaction detail view on row click if onView is available
        if (actions?.onView) {
          const typedTransaction = transaction as InventoryTransactionColumnData;
          actions.onView(typedTransaction);
        }
      }}
    />
  );
}

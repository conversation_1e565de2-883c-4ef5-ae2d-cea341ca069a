"use client";

import Link from "next/link";
import { useRouter } from "next/navigation";
import { useMemo } from "react";

import { Button } from "@/app/components/forms/Button";
import { StandardizedTable } from '@/app/components/tables/StandardizedTable';
import { createAssembliesColumns, AssemblyColumnData, AssembliesTableActions } from '@/app/components/data-display/data-table/column-definitions';
import { cn } from "@/app/lib/utils";
import { Layers } from "lucide-react";

import { useTheme } from '@/app/contexts/ThemeContext';
import { AssembliesTableProps, Assembly } from './types';
import { countHierarchicalParts } from '@/app/utils/assemblyDataTransform';
import { HierarchicalPartsList } from '@/app/components/parts/HierarchicalPartsList';

/**
 * Client component implementation of AssembliesTable using DataTable
 * Handles all interactive logic and rendering with modern DataTable component
 */
export default function AssembliesTableClient({
  assemblies,
  simple = false,
  isLoading = false,
  error = null
}: AssembliesTableProps & { isLoading?: boolean; error?: string | null }) {
  const router = useRouter();
  const { theme } = useTheme();

  /**
   * Handle refresh after actions
   */
  const handleRefresh = () => {
    router.refresh();
  };

  // Transform assemblies data for DataTable
  // IMPORTANT: Preserve the hierarchical structure for consistent parts counting
  const tableData: AssemblyColumnData[] = useMemo(() => {
    return assemblies.map(assembly => ({
      _id: assembly._id,
      assemblyCode: assembly.assemblyCode,
      name: assembly.name,
      description: assembly.description || '',
      status: assembly.status || 'active',
      // Preserve the original hierarchical structure instead of flattening
      // This ensures countHierarchicalParts works correctly in both card and table views
      partsRequired: assembly.partsRequired || [],
      // Add other fields as needed for the DataTable
      productId: assembly.productId,
      parentId: assembly.parentId,
      isTopLevel: assembly.isTopLevel,
      version: assembly.version,
      manufacturingInstructions: assembly.manufacturingInstructions,
      estimatedBuildTime: assembly.estimatedBuildTime,
      createdAt: assembly.createdAt,
      updatedAt: assembly.updatedAt,
    }));
  }, [assemblies]);

  // Define actions for the DataTable
  const actions: AssembliesTableActions = useMemo(() => ({
    onRefresh: handleRefresh,
  }), []);

  // Create columns with actions
  const columns = useMemo(() => createAssembliesColumns(actions, simple), [actions, simple]);

  // No longer need the handleDelete function as we're using the DeleteAssemblyAction component

  if (assemblies.length === 0) {
    return (
      <div className="p-8 text-center">
        <Layers className="h-12 w-12 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-medium mb-2">No Assemblies Found</h3>
        <p className="text-sm text-muted-foreground mb-4">
          There are no assemblies in the system yet.
        </p>
        <Button asChild>
          <Link href="/assemblies/create">Create Assembly</Link>
        </Button>
      </div>
    );
  }

  return (
    <StandardizedTable
      data={tableData}
      columns={columns}
      searchPlaceholder="Search assemblies..."
      enableSearch={true}
      enableViewToggle={false}
      enableSorting={true}
      enableFiltering={true}
      enableGlobalSearch={false} // Using StandardizedTable's search instead
      enablePagination={true}
      enableColumnVisibility={false}
      mobileDisplayMode="cards"
      density="normal"
      initialPagination={{ pageIndex: 0, pageSize: 20 }}
      pageSizeOptions={[10, 20, 50, 100]}
      caption={`${simple ? 'Simple' : 'Detailed'} assemblies table with ${assemblies.length} items`}
      onRowClick={(assembly: unknown) => {
        // Row click navigation removed - users should use the view button/modal
        const typedAssembly = assembly as AssemblyColumnData;
        console.log('Assembly row clicked:', typedAssembly._id);
      }}
      renderRowActions={(assembly: unknown) => {
        const typedAssembly = assembly as AssemblyColumnData;
        return (
          // Expandable row content for parts details
          <div className="p-4 bg-muted/50 rounded-lg">
            <h4 className="font-semibold mb-3 flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Parts Required ({countHierarchicalParts(typedAssembly.partsRequired || [])})
            </h4>
            {typedAssembly.partsRequired && typedAssembly.partsRequired.length > 0 ? (
              <div className="space-y-2 max-h-60 overflow-y-auto">
                <HierarchicalPartsList parts={typedAssembly.partsRequired} />
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">No parts defined for this assembly.</p>
            )}
          </div>
        );
      }}
      isLoading={isLoading}
      error={error ? new Error(error) : null}
    />
  );
}


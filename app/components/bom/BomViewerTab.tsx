'use client';

import { EnhancedB<PERSON>Viewer, type ModalComponentItem, hasBomData, getBomSummary } from './EnhancedBomViewer';
import { useTheme } from '@/app/contexts/ThemeContext';
import { cn } from '@/app/lib/utils';
import { Layers, Package, AlertTriangle, TrendingDown } from 'lucide-react';
import { Badge } from '@/app/components/data-display/badge';

/**
 * Props for BomViewerTab component
 */
interface BomViewerTabProps {
  /**
   * Components to display in the BOM viewer
   */
  components: ModalComponentItem[];
  
  /**
   * Initial expansion level
   */
  level?: number;
  
  /**
   * Whether components should be initially expanded
   */
  initiallyExpanded?: boolean;
  
  /**
   * Parent ID for hierarchical display
   */
  parentId?: string | null;
  
  /**
   * Enable search functionality
   */
  enableSearch?: boolean;
  
  /**
   * Custom height for the tree container (default: 500px)
   */
  height?: string;
  
  /**
   * Callback for lazy loading assembly components
   */
  onLoadAssemblyComponents?: (assemblyId: string) => Promise<ModalComponentItem[]>;
  
  /**
   * Loading state
   */
  isLoading?: boolean;
  
  /**
   * Error state
   */
  error?: string | null;
  
  /**
   * Show summary statistics
   */
  showSummary?: boolean;
  
  /**
   * Custom className
   */
  className?: string;
}

/**
 * BOM Viewer Tab Component
 * Designed to be integrated as a tab within BaseViewModal
 * Provides enhanced BOM visualization with summary statistics
 */
export function BomViewerTab({
  components,
  level = 0,
  initiallyExpanded = true,
  parentId = null,
  enableSearch = true,
  height = '500px',
  onLoadAssemblyComponents,
  isLoading = false,
  error = null,
  showSummary = true,
  className = ''
}: BomViewerTabProps) {
  const { theme } = useTheme();

  // Check if we have BOM data
  const hasData = hasBomData(components);
  
  // Get summary statistics
  const summary = hasData ? getBomSummary(components) : null;

  // If no data, show empty state
  if (!hasData && !isLoading && !error) {
    return (
      <div className={cn(
        "flex flex-col items-center justify-center py-12 text-muted-foreground",
        className
      )}>
        <Layers className="h-16 w-16 mb-4 opacity-50" />
        <h3 className="text-lg font-medium mb-2 text-foreground">No BOM Data Available</h3>
        <p className="text-sm text-center max-w-md">
          This item doesn't have any Bill of Materials data or components associated with it.
        </p>
      </div>
    );
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Compact Summary Statistics */}
      {showSummary && summary && (
        <div className="flex flex-wrap items-center gap-3 p-3 rounded-lg bg-muted/30 border border-border">
          <div className="flex items-center gap-2">
            <Layers className="h-4 w-4 text-primary" />
            <span className="text-sm font-medium text-foreground">
              {summary.totalComponents} Components
            </span>
          </div>

          <div className="h-4 w-px bg-border" />

          <div className="flex items-center gap-2">
            <Package className="h-4 w-4 text-green-600 dark:text-green-400" />
            <span className="text-sm font-medium text-foreground">
              {summary.totalAssemblies} Assemblies
            </span>
          </div>

          {summary.outOfStockItems > 0 && (
            <>
              <div className="h-4 w-px bg-border" />
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-destructive" />
                <span className="text-sm font-medium text-destructive">
                  {summary.outOfStockItems} Out of Stock
                </span>
              </div>
            </>
          )}

          {summary.lowStockItems > 0 && (
            <>
              <div className="h-4 w-px bg-border" />
              <div className="flex items-center gap-2">
                <TrendingDown className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
                <span className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                  {summary.lowStockItems} Low Stock
                </span>
              </div>
            </>
          )}
        </div>
      )}

      {/* Enhanced BOM Viewer */}
      <EnhancedBomViewer
        components={components}
        level={level}
        initiallyExpanded={initiallyExpanded}
        parentId={parentId}
        enableSearch={enableSearch}
        height={height}
        {...(onLoadAssemblyComponents && { onLoadAssemblyComponents })}
        isLoading={isLoading}
        error={error}
      />

      {/* Stock Status Legend */}
      {hasData && (
        <div className="flex flex-wrap items-center gap-2 pt-2 border-t border-border">
          <span className="text-xs text-muted-foreground">
            Legend:
          </span>
          <Badge variant="success" className="text-xs px-2 py-0.5">In Stock</Badge>
          <Badge variant="warning" className="text-xs px-2 py-0.5">Low Stock</Badge>
          <Badge variant="destructive" className="text-xs px-2 py-0.5">Out of Stock</Badge>
        </div>
      )}
    </div>
  );
}

export default BomViewerTab;

"use client";

import { <PERSON>ert, AlertDescription } from '@/app/components/data-display/alert';
import { Badge } from '@/app/components/data-display/badge';
import { Button } from '@/app/components/forms/Button';
import { Input } from '@/app/components/forms/Input';
import { Label } from '@/app/components/forms/label';
import { Switch } from '@/app/components/forms/switch';
import { Textarea } from '@/app/components/forms/Textarea/Textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/app/components/layout/cards/card';
import { useTheme } from '@/app/contexts/ThemeContext';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Building2, MapPin, Package, User, Phone, Hash } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { toast } from 'sonner';

import {
    WarehouseFormData,
    WarehouseFormProps,
    warehouseFormSchema,
    transformFormDataToApiRequest,
    LocationFormData
} from './types';
import LocationManagement from './LocationManagement';
import { LocationApiService, CreateLocationRequest } from '@/app/utils/locationApi';

/**
 * Client component implementation of WarehouseForm
 * Handles form validation, submission, and rendering
 */
export default function WarehouseFormClient({
  onSubmit,
  onClose,
  initialData,
  isEdit = false,
  title,
  isLoading = false,
  error
}: WarehouseFormProps) {
  const { theme } = useTheme();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(error || null);
  const [isLoadingLocations, setIsLoadingLocations] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors, isDirty }
  } = useForm<WarehouseFormData>({
    resolver: zodResolver(warehouseFormSchema),
    defaultValues: {
      _id: initialData?._id || undefined,
      location_id: initialData?.location_id || '',
      name: initialData?.name || '',
      location: initialData?.location || '',
      capacity: initialData?.capacity || 0,
      manager: initialData?.manager || '',
      contact: initialData?.contact || '',
      isBinTracked: initialData?.isBinTracked || false,
      description: initialData?.description || '',
      isActive: initialData?.isActive ?? true,
      locations: initialData?.locations || [],
    }
  });

  // Watch form values for dynamic updates
  const watchedValues = watch();

  // Reset form when initialData changes
  useEffect(() => {
    if (initialData) {
      reset({
        _id: initialData._id || undefined,
        location_id: initialData.location_id || '',
        name: initialData.name || '',
        location: initialData.location || '',
        capacity: initialData.capacity || 0,
        manager: initialData.manager || '',
        contact: initialData.contact || '',
        isBinTracked: initialData.isBinTracked || false,
        description: initialData.description || '',
        isActive: initialData.isActive ?? true,
        locations: initialData.locations || [], // Include locations in form reset
      });
    }
  }, [initialData, reset]);

  // Update form error when prop changes
  useEffect(() => {
    setFormError(error || null);
  }, [error]);

  // Fetch existing locations when editing a warehouse
  useEffect(() => {
    const fetchLocations = async () => {
      if (!isEdit || !initialData?._id) {
        return;
      }

      setIsLoadingLocations(true);

      try {
        const response = await fetch(`/api/warehouses/${initialData._id}/locations`);

        if (!response.ok) {
          throw new Error(`Failed to fetch locations: ${response.status}`);
        }

        const data = await response.json();

        if (data.data && Array.isArray(data.data)) {
          // Update the form with fetched locations
          setValue('locations', data.data);
        } else {
          setValue('locations', []);
        }
      } catch (error) {
        console.error('Error fetching warehouse locations:', error);
        toast.error('Failed to load existing locations');
      } finally {
        setIsLoadingLocations(false);
      }
    };

    fetchLocations();
  }, [isEdit, initialData?._id, setValue]);

  // Generate location ID if not provided (for new warehouses)
  const generateLocationId = () => {
    const prefix = 'WH';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
  };

  // Handle form submission
  const onFormSubmit = handleSubmit(async (data) => {
    try {
      setIsSubmitting(true);
      setFormError(null);

      // Generate location_id if not provided and not editing
      if (!isEdit && !data.location_id) {
        data.location_id = generateLocationId();
      }

      console.log('[WarehouseForm] Submitting form data:', data);

      // Submit the form data (including locations) to parent component
      await onSubmit(data as WarehouseFormData);

      toast.success(isEdit ? 'Warehouse updated successfully' : 'Warehouse created successfully');
      onClose();
    } catch (error) {
      console.error('Error submitting warehouse form:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to submit form. Please try again.';
      setFormError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  });

  const formTitle = title || (isEdit ? 'Edit Warehouse' : 'Create New Warehouse');

  return (
    <div className="space-y-6">
      {/* Form Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Building2 className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-semibold text-foreground">{formTitle}</h2>
        </div>
        {isEdit && initialData?.location_id && (
          <Badge variant="outline" className="text-sm">
            {initialData.location_id}
          </Badge>
        )}
      </div>

      {/* Error Alert */}
      {formError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{formError}</AlertDescription>
        </Alert>
      )}

      {/* Form */}
      <form id="warehouse-form" onSubmit={onFormSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Building2 className="h-5 w-5" />
                <span>Basic Information</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Warehouse Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center space-x-1">
                  <span>Warehouse Name</span>
                  <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="name"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="name"
                      placeholder="Enter warehouse name"
                      disabled={isSubmitting || isLoading}
                    />
                  )}
                />
              </div>

              {/* Location ID */}
              <div className="space-y-2">
                <Label htmlFor="location_id" className="flex items-center space-x-1">
                  <Hash className="h-4 w-4" />
                  <span>Location ID</span>
                  {!isEdit && <span className="text-sm text-muted-foreground">(auto-generated if empty)</span>}
                </Label>
                <Controller
                  name="location_id"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="location_id"
                      placeholder={isEdit ? "Location ID" : "Auto-generated if empty"}
                      disabled={isSubmitting || isLoading || isEdit} // Disable editing location_id
                    />
                  )}
                />
              </div>

              {/* Location Details */}
              <div className="space-y-2">
                <Label htmlFor="location" className="flex items-center space-x-1">
                  <MapPin className="h-4 w-4" />
                  <span>Location Details</span>
                  <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="location"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      id="location"
                      placeholder="Enter address and location details"
                      disabled={isSubmitting || isLoading}
                      rows={3}
                    />
                  )}
                />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      value={field.value || ''}
                      id="description"
                      placeholder="Optional description"
                      disabled={isSubmitting || isLoading}
                      rows={2}
                    />
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Operations & Contact Card */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Operations & Contact</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Manager */}
              <div className="space-y-2">
                <Label htmlFor="manager" className="flex items-center space-x-1">
                  <User className="h-4 w-4" />
                  <span>Manager</span>
                  <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="manager"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="manager"
                      placeholder="Enter manager name"
                      disabled={isSubmitting || isLoading}
                    />
                  )}
                />
              </div>

              {/* Contact */}
              <div className="space-y-2">
                <Label htmlFor="contact" className="flex items-center space-x-1">
                  <Phone className="h-4 w-4" />
                  <span>Contact Information</span>
                  <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="contact"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="contact"
                      placeholder="Phone, email, or other contact info"
                      disabled={isSubmitting || isLoading}
                    />
                  )}
                />
              </div>

              {/* Capacity */}
              <div className="space-y-2">
                <Label htmlFor="capacity" className="flex items-center space-x-1">
                  <Package className="h-4 w-4" />
                  <span>Storage Capacity</span>
                  <span className="text-red-500">*</span>
                </Label>
                <Controller
                  name="capacity"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="capacity"
                      type="number"
                      min="0"
                      step="1"
                      placeholder="Enter storage capacity"
                      disabled={isSubmitting || isLoading}
                      onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                    />
                  )}
                />
              </div>

              {/* Settings */}
              <div className="space-y-4 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="isBinTracked">Bin Tracking</Label>
                    <p className="text-sm text-muted-foreground">
                      Enable detailed bin-level inventory tracking
                    </p>
                  </div>
                  <Controller
                    name="isBinTracked"
                    control={control}
                    render={({ field }) => (
                      <Switch
                        id="isBinTracked"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isSubmitting || isLoading}
                      />
                    )}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label htmlFor="isActive">Active Status</Label>
                    <p className="text-sm text-muted-foreground">
                      Whether this warehouse is currently active
                    </p>
                  </div>
                  <Controller
                    name="isActive"
                    control={control}
                    render={({ field }) => (
                      <Switch
                        id="isActive"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isSubmitting || isLoading}
                      />
                    )}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Management Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <MapPin className="h-5 w-5" />
                <span>Warehouse Locations</span>
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                Manage specific locations within this warehouse for precise inventory tracking
              </p>
            </CardHeader>
            <CardContent>
              <Controller
                name="locations"
                control={control}
                render={({ field }) => (
                  <LocationManagement
                    locations={field.value || []}
                    onLocationsChange={field.onChange}
                    disabled={isSubmitting || isLoading || isLoadingLocations}
                    isLoading={isLoadingLocations}
                  />
                )}
              />
            </CardContent>
          </Card>
        </div>

        {/* Form Footer */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button
            variant="outline"
            onClick={onClose}
            type="button"
            disabled={isSubmitting || isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || isLoading || (!isEdit && !isDirty)}
            className="min-w-[120px]"
          >
            {isSubmitting || isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                <span>{isEdit ? 'Updating...' : 'Creating...'}</span>
              </div>
            ) : (
              <span>{isEdit ? 'Update Warehouse' : 'Create Warehouse'}</span>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}

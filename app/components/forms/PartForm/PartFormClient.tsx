"use client";

import { But<PERSON> } from '@/app/components/forms/Button';
import { Checkbox } from '@/app/components/forms/checkbox';
import { FormContainer } from '@/app/components/forms/form-container';
import { Input } from '@/app/components/forms/Input';
import { Label } from '@/app/components/forms/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/forms/Select';
import { Switch } from '@/app/components/forms/switch';
import { Card, CardContent } from '@/app/components/layout/cards/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/app/components/navigation/tabs';
import { useTheme } from '@/app/contexts/ThemeContext';
import { getApiUrl } from '@/app/utils/env';
import { zodResolver } from '@hookform/resolvers/zod';
import { AlertCircle, Database, Edit2, Package, Plus, Settings, Trash2 } from 'lucide-react';
import React, { ChangeEvent, useEffect, useState } from 'react';
import { Controller, useFieldArray, useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';

import {
    PartFormData,
    PartFormProps,
    partFormSchema,
    SubPartData
} from './types';

/**
 * Client component implementation of PartForm
 * Handles form validation, submission, and rendering
 */
export default function PartFormClient({
  onSubmit,
  onClose,
  initialData,
  isEdit = false,
  title
}: PartFormProps) {
  const { theme } = useTheme();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('general');
  const [availableParts, setAvailableParts] = useState<Array<{ _id: string, name: string }>>([]);
  const [suppliers, setSuppliers] = useState<Array<{ _id: string, name: string }>>([]);
  const [warehouses, setWarehouses] = useState<Array<{ _id: string, name: string }>>([]);
  const [isLoadingParts, setIsLoadingParts] = useState(false);
  const [isLoadingSuppliers, setIsLoadingSuppliers] = useState(false);
  const [isLoadingWarehouses, setIsLoadingWarehouses] = useState(false);
  const [showSubPartForm, setShowSubPartForm] = useState(false);
  const [editingSubPartIndex, setEditingSubPartIndex] = useState<number | null>(null);
  const [actualMongoId, setActualMongoId] = useState<string | undefined>(initialData?._id);

  // Store the original supplier ID to prevent it from being lost during form resets
  // Using useRef to persist across component re-mounts
  const originalSupplierIdRef = React.useRef<string>('');
  const originalWarehouseIdRef = React.useRef<string>('');

  // Track if form has been initialized to prevent multiple reset() calls
  const hasInitialized = React.useRef<boolean>(false);

  /**
   * Generates a unique part ID using UUID
   * @returns A unique part ID string
   */
  const generatePartId = () => {
    return uuidv4();
  };

  // Debug: Log initial data for troubleshooting and capture original IDs
  React.useEffect(() => {
    if (isEdit && initialData) {
      console.log('[PartFormClient] Initial data received:', {
        supplierId: initialData.supplierId,
        warehouseId: initialData.inventory?.warehouseId,
        partNumber: initialData.partNumber,
        businessName: initialData.businessName
      });

      // Capture the original supplier ID to prevent it from being lost
      if (initialData.supplierId && !originalSupplierIdRef.current) {
        originalSupplierIdRef.current = initialData.supplierId;
        console.log('[PartFormClient] Captured original supplier ID:', initialData.supplierId);
      }

      // Capture the original warehouse ID to prevent it from being lost
      if (initialData.inventory?.warehouseId && !originalWarehouseIdRef.current) {
        originalWarehouseIdRef.current = initialData.inventory.warehouseId;
        console.log('[PartFormClient] Captured original warehouse ID:', initialData.inventory.warehouseId);
      }
    }
  }, [isEdit, initialData]);



  // Initialize form with react-hook-form and zod validation
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<PartFormData>({
    resolver: zodResolver(partFormSchema) as any, // Type assertion to resolve Zod/React Hook Form type mismatch
    defaultValues: {
      _id: (isEdit && initialData?.partNumber) ? initialData.partNumber : (initialData?._id || generatePartId()),
      name: initialData?.name ?? '',
      businessName: initialData?.businessName ?? null, // NEW FIELD: Human-readable business name
      partNumber: initialData?.partNumber ?? '',
      description: initialData?.description ?? '',
      technicalSpecs: initialData?.technicalSpecs ?? '',
      isManufactured: initialData?.isManufactured ?? false,
      reorderLevel: initialData?.reorderLevel ?? null,
      status: initialData?.status ?? 'active',
      inventory: {
        stockLevels: {
          raw: initialData?.inventory?.stockLevels?.raw ?? 0,
          hardening: initialData?.inventory?.stockLevels?.hardening ?? 0,
          grinding: initialData?.inventory?.stockLevels?.grinding ?? 0,
          finished: initialData?.inventory?.stockLevels?.finished ?? 0,
          rejected: initialData?.inventory?.stockLevels?.rejected ?? 0,
        },
        lastStockUpdate: initialData?.inventory?.lastStockUpdate ?? null,
        warehouseId: initialData?.inventory?.warehouseId ?? '',
        safetyStockLevel: initialData?.inventory?.safetyStockLevel ?? 0,
        maximumStockLevel: initialData?.inventory?.maximumStockLevel ?? 1000,
        averageDailyUsage: initialData?.inventory?.averageDailyUsage ?? 0,
        abcClassification: initialData?.inventory?.abcClassification ?? 'C',
      },
      isAssembly: initialData?.isAssembly ?? false,
      subParts: initialData?.subParts?.map(sp => ({
        partId: sp.partId, // Use canonical partId
        quantity: sp.quantity
      })) || [],
      schemaVersion: initialData?.schemaVersion ?? 1,
      supplierId: initialData?.supplierId ?? '',
      unitOfMeasure: initialData?.unitOfMeasure ?? 'pcs',
      costPrice: initialData?.costPrice ?? 0,
      categoryId: initialData?.categoryId ?? '',
    }
  });

  // INITIAL FORM SETUP EFFECT
  // This effect runs once to set up the form with initial data
  React.useEffect(() => {
    // Only run once for initial form setup
    if (!initialData || hasInitialized.current) return;

    console.log('[PartFormClient] INITIAL SETUP: Setting up form with initial data:', {
      supplierId: initialData.supplierId,
      warehouseId: initialData.inventory?.warehouseId,
      partNumber: initialData.partNumber,
      businessName: initialData.businessName,
      hasInitialized: hasInitialized.current
    });

    // Build form data with original values - no complex resolution logic here
    const formData = {
      _id: (isEdit && initialData?.partNumber) ? initialData.partNumber : (initialData?._id || generatePartId()),
      name: initialData?.name ?? '',
      businessName: initialData?.businessName ?? null,
      partNumber: initialData?.partNumber ?? '',
      description: initialData?.description ?? '',
      technicalSpecs: initialData?.technicalSpecs ?? '',
      isManufactured: initialData?.isManufactured ?? false,
      reorderLevel: initialData?.reorderLevel ?? null,
      status: initialData?.status ?? 'active',
      inventory: {
        stockLevels: {
          raw: initialData?.inventory?.stockLevels?.raw ?? 0,
          hardening: initialData?.inventory?.stockLevels?.hardening ?? 0,
          grinding: initialData?.inventory?.stockLevels?.grinding ?? 0,
          finished: initialData?.inventory?.stockLevels?.finished ?? 0,
          rejected: initialData?.inventory?.stockLevels?.rejected ?? 0,
        },
        lastStockUpdate: initialData?.inventory?.lastStockUpdate ?? null,
        warehouseId: initialData?.inventory?.warehouseId ?? '',
        safetyStockLevel: initialData?.inventory?.safetyStockLevel ?? undefined,
        maximumStockLevel: initialData?.inventory?.maximumStockLevel ?? undefined,
        averageDailyUsage: initialData?.inventory?.averageDailyUsage ?? undefined,
        abcClassification: initialData?.inventory?.abcClassification ?? undefined,
      },
      isAssembly: initialData?.isAssembly ?? false,
      subParts: initialData?.subParts?.map(sp => ({
        partId: sp.partId,
        quantity: sp.quantity
      })) || [],
      schemaVersion: initialData?.schemaVersion ?? 1,
      supplierId: initialData?.supplierId ?? '',
      unitOfMeasure: initialData?.unitOfMeasure ?? 'pcs',
      costPrice: initialData?.costPrice ?? undefined,
      categoryId: initialData?.categoryId ?? '',
    };

    console.log('[PartFormClient] INITIAL SETUP: Form data set:', {
      supplierId: formData.supplierId,
      warehouseId: formData.inventory.warehouseId,
      partNumber: formData.partNumber,
      businessName: formData.businessName
    });

    reset(formData);
    hasInitialized.current = true;
  }, [initialData, isEdit, reset, generatePartId]);

  // SUPPLIER DROPDOWN UPDATE EFFECT
  // This effect handles supplier dropdown updates when supplier data loads
  // Uses setValue instead of reset to avoid overwriting other form fields
  React.useEffect(() => {
    if (!isEdit || !initialData?.supplierId || isLoadingSuppliers || !hasInitialized.current) return;

    const currentSupplierValue = watch('supplierId');
    const effectiveSupplierIdToUse = originalSupplierIdRef.current || initialData.supplierId;

    // Only update if the current form value is empty but we have a supplier ID to set
    if (!currentSupplierValue && effectiveSupplierIdToUse) {
      console.log('[PartFormClient] SUPPLIER UPDATE: Updating supplier dropdown after data load:', {
        effectiveSupplierIdToUse,
        currentFormValue: currentSupplierValue,
        suppliersLoaded: suppliers.length,
        hasInitialized: hasInitialized.current
      });

      // Always set the supplier ID, regardless of whether it's in the current list
      // This handles cases where supplier was deleted but we want to preserve the value
      setValue('supplierId', effectiveSupplierIdToUse);

      const matchingSupplier = suppliers.find(s =>
        String(s._id) === String(effectiveSupplierIdToUse)
      );

      if (matchingSupplier) {
        console.log('[PartFormClient] SUPPLIER UPDATE: Found matching supplier:', {
          supplierId: matchingSupplier._id,
          supplierName: matchingSupplier.name
        });
      } else {
        console.log('[PartFormClient] SUPPLIER UPDATE: Supplier not in current list (may be deleted):', {
          supplierId: effectiveSupplierIdToUse,
          availableSuppliers: suppliers.map(s => ({ _id: s._id, name: s.name }))
        });
      }
    }
  }, [isEdit, initialData?.supplierId, suppliers, isLoadingSuppliers]);

  // WAREHOUSE DROPDOWN UPDATE EFFECT
  // This effect handles warehouse dropdown updates when warehouse data loads
  // Uses setValue instead of reset to avoid overwriting other form fields
  React.useEffect(() => {
    if (!isEdit || !initialData?.inventory?.warehouseId || isLoadingWarehouses || !hasInitialized.current) return;

    const currentWarehouseValue = watch('inventory.warehouseId');
    const effectiveWarehouseIdToUse = originalWarehouseIdRef.current || initialData.inventory.warehouseId;

    // Only update if the current form value is empty but we have a warehouse ID to set
    if (!currentWarehouseValue && effectiveWarehouseIdToUse) {
      console.log('[PartFormClient] WAREHOUSE UPDATE: Updating warehouse dropdown after data load:', {
        effectiveWarehouseIdToUse,
        currentFormValue: currentWarehouseValue,
        warehousesLoaded: warehouses.length,
        hasInitialized: hasInitialized.current
      });

      // Check if the warehouse exists in the current list
      const matchingWarehouse = warehouses.find(w =>
        String(w._id) === String(effectiveWarehouseIdToUse)
      );

      if (matchingWarehouse) {
        setValue('inventory.warehouseId', matchingWarehouse._id);
        console.log('[PartFormClient] WAREHOUSE UPDATE: Found matching warehouse:', {
          warehouseId: matchingWarehouse._id,
          warehouseName: matchingWarehouse.name
        });
      } else if (warehouses.length > 0) {
        // Fallback to first warehouse if original not found
        setValue('inventory.warehouseId', warehouses[0]!._id);
        console.warn('[PartFormClient] WAREHOUSE UPDATE: Original warehouse not found, using first available:', {
          originalId: effectiveWarehouseIdToUse,
          fallbackId: warehouses[0]!._id,
          fallbackName: warehouses[0]!.name
        });
      } else {
        // Set the original warehouse ID even if not found in current list
        setValue('inventory.warehouseId', effectiveWarehouseIdToUse);
        console.log('[PartFormClient] WAREHOUSE UPDATE: Using original warehouse ID (not in current list):', {
          warehouseId: effectiveWarehouseIdToUse
        });
      }
    }
  }, [isEdit, initialData?.inventory?.warehouseId, warehouses, isLoadingWarehouses]);

  // Watch for changes to isAssembly to show/hide sub-parts section
  const isAssembly = watch('isAssembly');

  // Use field array to manage sub-parts
  const { fields, append, remove, update } = useFieldArray({
    control,
    name: 'subParts',
  });

  // Fetch available parts for sub-part selection
  useEffect(() => {
    const fetchParts = async () => {
      if (isAssembly) {
        try {
          setIsLoadingParts(true);
          const response = await fetch(getApiUrl('/api/parts'));
          if (!response.ok) {
            throw new Error('Failed to fetch parts');
          }
          const data = await response.json();
          // Filter out the current part if editing
          const filteredParts = data.data.filter((part: any) =>
            !isEdit || part._id !== initialData?._id
          );
          setAvailableParts(filteredParts);
        } catch (error) {
          console.error('Error fetching parts:', error);
          toast.error('Failed to load parts for assembly');
        } finally {
          setIsLoadingParts(false);
        }
      }
    };

    fetchParts();
  }, [isAssembly, isEdit, initialData?._id]);

  // Fetch suppliers for supplier selection with robust error handling
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setIsLoadingSuppliers(true);
        const response = await fetch(getApiUrl('/api/suppliers'));
        if (!response.ok) {
          throw new Error(`Failed to fetch suppliers: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        if (data.data && Array.isArray(data.data)) {
          console.log('[PartFormClient] Suppliers loaded:', {
            count: data.data.length,
            suppliers: data.data.map((s: any) => ({ _id: s._id, name: s.name }))
          });
          setSuppliers(data.data);
        } else {
          console.warn('[PartFormClient] Invalid supplier data format:', data);
          setSuppliers([]); // Set empty array as fallback
        }
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        // Set empty array to allow form to function without suppliers
        setSuppliers([]);
        // Don't show error toast as suppliers are optional
        console.warn('[PartFormClient] Continuing without supplier data due to API failure');
      } finally {
        setIsLoadingSuppliers(false);
      }
    };

    fetchSuppliers();
  }, []);

  // Fetch warehouses for warehouse selection with robust error handling
  useEffect(() => {
    const fetchWarehouses = async () => {
      try {
        setIsLoadingWarehouses(true);
        const response = await fetch(getApiUrl('/api/warehouses'));
        if (!response.ok) {
          throw new Error(`Failed to fetch warehouses: ${response.status} ${response.statusText}`);
        }
        const result = await response.json();
        if (result.data && Array.isArray(result.data)) {
          console.log('[PartFormClient] Warehouses loaded:', {
            count: result.data.length,
            warehouses: result.data.map((w: any) => ({ _id: w._id, name: w.name }))
          });
          setWarehouses(result.data);
        } else {
          console.warn('[PartFormClient] Invalid warehouse data format:', result);
          setWarehouses([]); // Set empty array as fallback
        }
      } catch (error) {
        console.error('Error fetching warehouses:', error);
        // Set empty array to allow form to function without warehouses
        setWarehouses([]);
        // Show warning but don't block the form
        console.warn('[PartFormClient] Continuing without warehouse data due to API failure');
        // Only show toast if this is critical for the user experience
        if (isEdit) {
          toast.error('Failed to load warehouses. Form will use existing values.');
        }
      } finally {
        setIsLoadingWarehouses(false);
      }
    };

    fetchWarehouses();
  }, [isEdit]);

  // Effect to store the actual MongoDB _id separately if editing
  useEffect(() => {
    if (isEdit && initialData?._id) {
      setActualMongoId(initialData._id);
    }
  }, [isEdit, initialData]);

  /**
   * Handles adding a new sub-part to the assembly
   */
  const handleAddSubPart = (partId: string, quantity: number) => {
    // Find the part in availableParts
    const part = availableParts.find(p => p._id === partId);
    if (!part) {
      toast.error('Selected part not found');
      return;
    }

    if (editingSubPartIndex !== null) {
      // Update existing sub-part
      update(editingSubPartIndex, { partId: partId, quantity });
      setEditingSubPartIndex(null);
    } else {
      // Add new sub-part
      append({ partId: partId, quantity });
    }

    setShowSubPartForm(false);
  };

  /**
   * Handles editing an existing sub-part
   */
  const handleEditSubPart = (index: number) => {
    setEditingSubPartIndex(index);
    setShowSubPartForm(true);
  };

  /**
   * Handles removing a sub-part from the assembly
   */
  const handleRemoveSubPart = (index: number) => {
    remove(index);
  };

  /**
   * Handles form submission
   * Submits data to parent component and closes the form on success
   */
  const onFormSubmit = handleSubmit(async (data) => {
    try {
      setIsSubmitting(true);
      setFormError(null);

      if (data.isAssembly && (!data.subParts || data.subParts.length === 0)) {
        setFormError('An assembly must have at least one sub-part');
        setActiveTab('advanced');
        setIsSubmitting(false);
        return;
      }

      // Explicitly construct submissionData to match PartFormData / Zod schema
      const submissionData: PartFormData = {
        _id: isEdit && actualMongoId ? actualMongoId : data._id,
        name: data.name, // required
        businessName: data.businessName || null, // NEW FIELD: Human-readable business name
        partNumber: data.partNumber || undefined, // optional string
        description: data.description || undefined,
        technicalSpecs: data.technicalSpecs || undefined,
        isManufactured: data.isManufactured, // boolean with default
        reorderLevel: data.reorderLevel,    // number | null
        status: data.status,                // enum with default
        isAssembly: data.isAssembly,        // boolean with default
        schemaVersion: data.schemaVersion === undefined && !isEdit ? 1 : data.schemaVersion, // Default to 1 if new and undefined
        subParts: data.subParts?.map((part: SubPartData) => ({
          partId: part.partId, // Use canonical partId
          quantity: part.quantity,
        })) || undefined, // optional array
        supplierId: data.supplierId || undefined,
        unitOfMeasure: data.unitOfMeasure || undefined,
        // For optional numbers (e.g. costPrice), if form sends null for "empty", convert to undefined for Zod .optional()
        costPrice: data.costPrice === null ? undefined : data.costPrice,
        categoryId: data.categoryId || undefined,
        inventory: { // inventory object is required
          stockLevels: {
            raw: data.inventory.stockLevels.raw,
            hardening: data.inventory.stockLevels.hardening,
            grinding: data.inventory.stockLevels.grinding,
            finished: data.inventory.stockLevels.finished,
            rejected: data.inventory.stockLevels.rejected,
          },
          // For z.date().nullable().optional(), null is fine, undefined if truly not set.
          lastStockUpdate: data.inventory.lastStockUpdate === null ? undefined : data.inventory.lastStockUpdate,
          // Include warehouseId for all parts since we have multiple locations
          warehouseId: data.inventory.warehouseId,
          safetyStockLevel: (data.inventory.safetyStockLevel === null || data.inventory.safetyStockLevel === undefined) ? 0 : data.inventory.safetyStockLevel,
          maximumStockLevel: (data.inventory.maximumStockLevel === null || data.inventory.maximumStockLevel === undefined) ? 1000 : data.inventory.maximumStockLevel,
          averageDailyUsage: (data.inventory.averageDailyUsage === null || data.inventory.averageDailyUsage === undefined) ? 0 : data.inventory.averageDailyUsage,
          abcClassification: data.inventory.abcClassification || 'C',
        },
      };

      console.log('[FRONTEND DEBUG] Submitting form data:', submissionData);
      await onSubmit(submissionData);
      onClose();
    } catch (error) {
      console.error('Error submitting form:', error);
      setFormError('Failed to submit form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  });

  // Form footer with cancel and submit buttons
  const formFooter = (
    <>
      <Button
        variant="outline"
        onClick={onClose}
        type="button"
      >
        Cancel
      </Button>
      <Button
        type="submit"
        form="part-form"
        disabled={isSubmitting}
      >
        {isEdit ? 'Update Part' : 'Create Part'}
      </Button>
    </>
  );

  // Error message component
  const FormError = ({ name, message }: { name: string, message: string | undefined }) => {
    if (!message) return null;

    return (
      <p className="mt-1 text-sm text-destructive flex items-center">
        <AlertCircle size={14} className="mr-1" />
        {message}
      </p>
    );
  };

  // Sub-part form component
  const SubPartForm = () => {
    const [selectedPartId, setSelectedPartId] = useState<string>(
      editingSubPartIndex !== null && fields[editingSubPartIndex]
        ? fields[editingSubPartIndex].partId
        : ''
    );
    const [quantity, setQuantity] = useState<number>(
      editingSubPartIndex !== null && fields[editingSubPartIndex]
        ? fields[editingSubPartIndex].quantity
        : 1
    );

    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault();
      if (!selectedPartId) {
        toast.error('Please select a part');
        return;
      }
      if (quantity < 1) {
        toast.error('Quantity must be at least 1');
        return;
      }
      handleAddSubPart(selectedPartId, quantity);
    };

    return (
      <Card className="mb-4 border border-input dark:border-dark-border">
        <CardContent className="p-4">
          <h3 className="text-lg font-medium mb-4">
            {editingSubPartIndex !== null ? 'Edit Sub-Part' : 'Add Sub-Part'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="partId">Select Part</Label>
              <select
                id="partId"
                value={selectedPartId}
                onChange={(e) => setSelectedPartId(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                required
                aria-label="Select a part for this assembly"
              >
                <option value="">Select a part...</option>
                {availableParts.map((part) => (
                  <option key={part._id} value={part._id}>
                    {part.name}
                  </option>
                ))}
              </select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity</Label>
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-3 py-2 bg-secondary dark:bg-dark-element rounded-l-md border border-input dark:border-dark-border"
                  aria-label="Decrease quantity"
                >
                  -
                </button>
                <input
                  id="quantity"
                  type="number"
                  min="1"
                  value={quantity}
                  onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                  className="flex h-10 w-20 text-center border-y border-input dark:border-dark-border bg-background px-3 py-2 text-sm focus-visible:outline-none dark:bg-dark-element/60 dark:text-dark-text-primary"
                  required
                  aria-label="Quantity of this part needed in the assembly"
                />
                <button
                  type="button"
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-3 py-2 bg-secondary dark:bg-dark-element rounded-r-md border border-input dark:border-dark-border"
                  aria-label="Increase quantity"
                >
                  +
                </button>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowSubPartForm(false);
                  setEditingSubPartIndex(null);
                }}
              >
                Cancel
              </Button>
              <Button type="submit">
                {editingSubPartIndex !== null ? 'Update' : 'Add'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex justify-center items-center z-[100] p-4">
      <FormContainer
        title={title || (isEdit ? 'Edit Part' : 'Add New Part')}
        isLoading={isSubmitting}
        error={formError}
        animate={true}
        footer={formFooter}
        className="max-w-3xl mx-auto"
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-3 mb-6">
            <TabsTrigger value="general" className="flex items-center gap-2">
              <Package size={16} />
              <span>General</span>
            </TabsTrigger>
            <TabsTrigger value="inventory" className="flex items-center gap-2">
              <Database size={16} />
              <span>Inventory</span>
            </TabsTrigger>
            <TabsTrigger value="advanced" className="flex items-center gap-2">
              <Settings size={16} />
              <span>Advanced</span>
            </TabsTrigger>
          </TabsList>

          <form id="part-form" onSubmit={onFormSubmit} className="space-y-6">
            {/* General Tab */}
            <TabsContent value="general" className="space-y-6">
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="_id">Part ID</Label>
                    <Controller
                      name="_id"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="_id"
                            readOnly={isEdit}
                            className={errors._id ? "border-destructive" : ""}
                          />
                          <FormError name="_id" message={errors._id?.message} />
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">Name</Label>
                    <Controller
                      name="name"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="name"
                            className={errors.name ? "border-destructive" : ""}
                          />
                          <FormError name="name" message={errors.name?.message} />
                        </div>
                      )}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="businessName">Business Name</Label>
                  <Controller
                    name="businessName"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          value={field.value || ''}
                          id="businessName"
                          placeholder="Human-readable name for this part (optional)"
                          className={errors.businessName ? "border-destructive" : ""}
                        />
                        <FormError name="businessName" message={errors.businessName?.message} />
                        <p className="text-sm text-muted-foreground">
                          A user-friendly name that will be displayed prominently in the inventory table
                        </p>
                      </div>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Controller
                    name="description"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <textarea
                          {...field}
                          id="description"
                          className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                          rows={3}
                        />
                        <FormError name="description" message={errors.description?.message} />
                      </div>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="technicalSpecs">Technical Specifications</Label>
                  <Controller
                    name="technicalSpecs"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <textarea
                          {...field}
                          id="technicalSpecs"
                          className="flex h-20 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg"
                          rows={3}
                        />
                        <FormError name="technicalSpecs" message={errors.technicalSpecs?.message} />
                      </div>
                    )}
                  />
                </div>

                <div className="flex items-center space-x-2">
                  <Controller
                    name="isManufactured"
                    control={control}
                    render={({ field }) => (
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="isManufactured"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                        <Label htmlFor="isManufactured" className="cursor-pointer">
                          Manufactured In-House
                        </Label>
                      </div>
                    )}
                  />
                </div>



                <div className="space-y-2">
                  <Label htmlFor="unitOfMeasure">Unit of Measure</Label>
                  <Controller
                    name="unitOfMeasure"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          id="unitOfMeasure"
                          placeholder="e.g., pcs, kg, meter"
                        />
                      </div>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="costPrice">Cost Price</Label>
                  <Controller
                    name="costPrice"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          id="costPrice"
                          type="number"
                          step="0.01"
                          placeholder="Cost per unit"
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    )}
                  />
                </div>
              </div>
            </TabsContent>

            {/* Inventory Tab */}
            <TabsContent value="inventory" className="space-y-6">
              {/* Stock Levels Section */}
              <div className="space-y-4">
                <div className="border-b pb-2">
                  <h3 className="text-lg font-medium">Stock Levels</h3>
                  <p className="text-sm text-muted-foreground">Track inventory across different manufacturing stages</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="inventory.stockLevels.raw">Raw Materials</Label>
                    <Controller
                      name="inventory.stockLevels.raw"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="inventory.stockLevels.raw"
                            type="number"
                            min="0"
                            className={errors.inventory?.stockLevels?.raw ? "border-destructive" : ""}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                          <FormError name="inventory.stockLevels.raw" message={errors.inventory?.stockLevels?.raw?.message} />
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="inventory.stockLevels.hardening">In Hardening</Label>
                    <Controller
                      name="inventory.stockLevels.hardening"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="inventory.stockLevels.hardening"
                            type="number"
                            min="0"
                            className={errors.inventory?.stockLevels?.hardening ? "border-destructive" : ""}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                          <FormError name="inventory.stockLevels.hardening" message={errors.inventory?.stockLevels?.hardening?.message} />
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="inventory.stockLevels.grinding">In Grinding</Label>
                    <Controller
                      name="inventory.stockLevels.grinding"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="inventory.stockLevels.grinding"
                            type="number"
                            min="0"
                            className={errors.inventory?.stockLevels?.grinding ? "border-destructive" : ""}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                          <FormError name="inventory.stockLevels.grinding" message={errors.inventory?.stockLevels?.grinding?.message} />
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="inventory.stockLevels.finished">Finished Goods</Label>
                    <Controller
                      name="inventory.stockLevels.finished"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="inventory.stockLevels.finished"
                            type="number"
                            min="0"
                            className={errors.inventory?.stockLevels?.finished ? "border-destructive" : ""}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                          <FormError name="inventory.stockLevels.finished" message={errors.inventory?.stockLevels?.finished?.message} />
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="inventory.stockLevels.rejected">Rejected</Label>
                    <Controller
                      name="inventory.stockLevels.rejected"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="inventory.stockLevels.rejected"
                            type="number"
                            min="0"
                            className={errors.inventory?.stockLevels?.rejected ? "border-destructive" : ""}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                          <FormError name="inventory.stockLevels.rejected" message={errors.inventory?.stockLevels?.rejected?.message} />
                        </div>
                      )}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="reorderLevel">Reorder Level</Label>
                    <Controller
                      name="reorderLevel"
                      control={control}
                      render={({ field }) => (
                        <div>
                          <Input
                            {...field}
                            id="reorderLevel"
                            type="number"
                            value={field.value === null ? '' : field.value}
                            className={errors.reorderLevel ? "border-destructive" : ""}
                            onChange={(e: ChangeEvent<HTMLInputElement>) => field.onChange(e.target.value === '' ? null : parseInt(e.target.value) || 0)}
                          />
                          <FormError name="reorderLevel" message={errors.reorderLevel?.message} />
                        </div>
                      )}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="inventory.warehouseId">
                  Warehouse *
                </Label>
                <Controller
                  name="inventory.warehouseId"
                  control={control}
                  rules={{
                    required: 'Warehouse selection is required'
                  }}
                  render={({ field }) => (
                    <div>
                      <Select
                        value={field.value || ''}
                        onValueChange={(value) => {
                          console.log('[PartFormClient] Warehouse selection changed:', {
                            newValue: value,
                            previousValue: field.value
                          });
                          field.onChange(value);
                        }}
                        disabled={isLoadingWarehouses}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder={isLoadingWarehouses ? "Loading warehouses..." : "Select a warehouse"} />
                        </SelectTrigger>
                        <SelectContent>
                          {warehouses.map((warehouse) => (
                            <SelectItem key={warehouse._id} value={warehouse._id}>
                              {warehouse.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {isLoadingWarehouses && (
                        <p className="text-xs text-muted-foreground dark:text-dark-text-secondary mt-1">
                          Loading warehouses...
                        </p>
                      )}
                      <FormError name="inventory.warehouseId" message={errors.inventory?.warehouseId?.message} />
                    </div>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="inventory.safetyStockLevel">Safety Stock Level</Label>
                  <Controller
                    name="inventory.safetyStockLevel"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          id="inventory.safetyStockLevel"
                          type="number"
                          placeholder="Minimum stock to avoid stockouts"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </div>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="inventory.maximumStockLevel">Maximum Stock Level</Label>
                  <Controller
                    name="inventory.maximumStockLevel"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          id="inventory.maximumStockLevel"
                          type="number"
                          placeholder="Maximum desired stock level"
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </div>
                    )}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="inventory.averageDailyUsage">Average Daily Usage</Label>
                  <Controller
                    name="inventory.averageDailyUsage"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          id="inventory.averageDailyUsage"
                          type="number"
                          step="0.01"
                          placeholder="Estimated daily consumption rate"
                          onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                        />
                      </div>
                    )}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="inventory.abcClassification">ABC Classification</Label>
                  <Controller
                    name="inventory.abcClassification"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <select
                          {...field}
                          id="inventory.abcClassification"
                          className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 dark:border-dark-border dark:bg-dark-element/60 dark:text-dark-text-primary dark:placeholder:text-dark-text-secondary/80 dark:focus-visible:ring-accent dark:focus-visible:ring-offset-dark-bg ${errors.inventory?.abcClassification ? "border-destructive" : ""}`}
                        >
                          <option value="">Select classification...</option>
                          <option value="A">A (High value)</option>
                          <option value="B">B (Medium value)</option>
                          <option value="C">C (Low value)</option>
                        </select>
                      </div>
                    )}
                  />
                </div>
              </div>


            </TabsContent>

            {/* Advanced Tab */}
            <TabsContent value="advanced" className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Controller
                    name="isAssembly"
                    control={control}
                    render={({ field }) => (
                      <div className="flex items-center space-x-2">
                        <Switch
                          id="isAssembly"
                          checked={field.value}
                          onCheckedChange={(checked) => {
                            field.onChange(checked);
                            // If turning off assembly, clear sub-parts
                            if (!checked && fields.length > 0) {
                              if (confirm('Removing assembly status will delete all sub-parts. Continue?')) {
                                // Remove all sub-parts
                                fields.forEach((_, index) => remove(index));
                              } else {
                                // Keep assembly status if user cancels
                                field.onChange(true);
                              }
                            }
                          }}
                          aria-label="Toggle assembly status"
                        />
                        <Label htmlFor="isAssembly" className="cursor-pointer">
                          This is an Assembly
                        </Label>
                      </div>
                    )}
                  />
                </div>

                {/* Sub-parts section - only visible when isAssembly is true */}
                {isAssembly && (
                  <div className="mt-6 space-y-4 border border-input dark:border-dark-border rounded-md p-4 bg-secondary/10 dark:bg-dark-element/20">
                    <div className="flex justify-between items-center">
                      <h3 className="text-lg font-medium">Sub-Parts</h3>
                      <Button
                        type="button"
                        size="sm"
                        onClick={() => {
                          setEditingSubPartIndex(null);
                          setShowSubPartForm(true);
                        }}
                        className="flex items-center"
                        aria-label="Add a sub-part to this assembly"
                      >
                        <Plus size={16} className="mr-1" />
                        Add Sub-Part
                      </Button>
                    </div>

                    {/* Show form for adding/editing sub-parts */}
                    {showSubPartForm && <SubPartForm />}

                    {/* List of sub-parts */}
                    {fields.length > 0 ? (
                      <div className="space-y-2">
                        {fields.map((field, index) => {
                          // Find the part name from availableParts
                          const part = availableParts.find(p => p._id === field.partId);
                          const partName = part ? part.name : field.partId;

                          return (
                            <Card key={field.id} className="border border-input dark:border-dark-border">
                              <CardContent className="p-3 flex justify-between items-center">
                                <div>
                                  <p className="font-medium">{partName}</p>
                                  <p className="text-sm text-muted-foreground dark:text-dark-text-secondary">
                                    Quantity: {field.quantity}
                                  </p>
                                </div>
                                <div className="flex space-x-2">
                                  <Button
                                    type="button"
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleEditSubPart(index)}
                                    aria-label={`Edit sub-part ${partName}`}
                                  >
                                    <Edit2 size={16} />
                                  </Button>
                                  <Button
                                    type="button"
                                    variant="destructive"
                                    size="sm"
                                    onClick={() => handleRemoveSubPart(index)}
                                    aria-label={`Remove sub-part ${partName}`}
                                  >
                                    <Trash2 size={16} />
                                  </Button>
                                </div>
                              </CardContent>
                            </Card>
                          );
                        })}
                      </div>
                    ) : (
                      <div className="text-center py-4 text-muted-foreground dark:text-dark-text-secondary">
                        {isLoadingParts ? (
                          <p>Loading parts...</p>
                        ) : (
                          <p>No sub-parts added yet. Add parts to this assembly.</p>
                        )}
                      </div>
                    )}

                    {/* Error message for sub-parts validation */}
                    {isAssembly && errors.subParts && (
                      <p className="text-destructive text-sm flex items-center">
                        <AlertCircle size={14} className="mr-1" />
                        An assembly must have at least one sub-part
                      </p>
                    )}
                  </div>
                )}

                <div className="space-y-2">
                  <Label htmlFor="supplierId">Supplier</Label>
                  <Controller
                    name="supplierId"
                    control={control}
                    render={({ field }) => {
                      // Get the current supplier value, ensuring it's a string
                      const currentSupplierValue = field.value || '';

                      // Check if we have a supplier ID but no matching supplier in the list
                      const hasSupplierIdButNotInList = currentSupplierValue &&
                        suppliers.length > 0 &&
                        !suppliers.find(s => String(s._id) === String(currentSupplierValue));

                      // Find the current supplier in the list to get its name
                      const currentSupplier = currentSupplierValue && suppliers.length > 0
                        ? suppliers.find(s => String(s._id) === String(currentSupplierValue))
                        : null;

                      console.log('[PartFormClient] Supplier dropdown render:', {
                        currentSupplierValue,
                        hasSupplierIdButNotInList,
                        currentSupplierName: currentSupplier?.name,
                        suppliersCount: suppliers.length,
                        isLoadingSuppliers
                      });

                      return (
                        <div>
                          <Select
                            value={currentSupplierValue || '__no_selection__'}
                            onValueChange={(value) => {
                              // Handle empty selection
                              const cleanValue = value === '__no_selection__' ? '' : (value || '');
                              field.onChange(cleanValue);
                              console.log('[PartFormClient] Supplier selection changed:', {
                                newValue: cleanValue,
                                previousValue: field.value
                              });
                            }}
                          >
                            <SelectTrigger
                              id="supplierId"
                              className={errors.supplierId ? "border-destructive" : ""}
                              aria-label="Supplier"
                            >
                              <SelectValue
                                placeholder={isLoadingSuppliers ? "Loading suppliers..." : "Select a supplier..."}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              {isLoadingSuppliers ? (
                                <SelectItem value="__loading__" disabled>Loading suppliers...</SelectItem>
                              ) : (
                                <>
                                  {/* Option to clear selection */}
                                  <SelectItem value="__no_selection__">
                                    <span className="text-muted-foreground">No supplier</span>
                                  </SelectItem>

                                  {/* Show current supplier even if not in the loaded list (handles API failures) */}
                                  {hasSupplierIdButNotInList && currentSupplierValue && (
                                    <SelectItem value={currentSupplierValue} disabled>
                                      {currentSupplierValue} (Current - not in list)
                                    </SelectItem>
                                  )}

                                  {/* Show available suppliers */}
                                  {suppliers.length > 0 ? (
                                    suppliers.map((supplier) => (
                                      <SelectItem key={supplier._id} value={supplier._id}>
                                        {supplier.name}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="__no_suppliers__" disabled>
                                      No suppliers available
                                    </SelectItem>
                                  )}
                                </>
                              )}
                            </SelectContent>
                          </Select>
                          {isLoadingSuppliers && (
                            <p className="text-xs text-muted-foreground dark:text-dark-text-secondary mt-1">
                              Loading suppliers...
                            </p>
                          )}
                          {hasSupplierIdButNotInList && currentSupplierValue && (
                            <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                              Current supplier ({currentSupplierValue}) not found in available suppliers.
                            </p>
                          )}
                          {currentSupplier && (
                            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                              Selected: {currentSupplier.name}
                            </p>
                          )}
                          <FormError name="supplierId" message={errors.supplierId?.message} />
                        </div>
                      );
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="schemaVersion">Schema Version</Label>
                  <Controller
                    name="schemaVersion"
                    control={control}
                    render={({ field }) => (
                      <div>
                        <Input
                          {...field}
                          id="schemaVersion"
                          type="number"
                          min="1"
                          readOnly
                          onChange={(e) => field.onChange(Number(e.target.value))}
                          className={errors.schemaVersion ? "border-destructive" : ""}
                          aria-label="Schema Version"
                        />
                        <FormError name="schemaVersion" message={errors.schemaVersion?.message} />
                      </div>
                    )}
                  />
                </div>
              </div>
            </TabsContent>
          </form>
        </Tabs>
      </FormContainer>
    </div>
  );
}
"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cn } from "@/app/lib/utils";
import { ButtonProps, buttonVariants } from "./types";

/**
 * Client component for Button that handles forwarded refs and dynamic styling
 */
const ButtonClient = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, type = "button", ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        type={type}
        {...props}
      />
    );
  }
);
ButtonClient.displayName = "ButtonClient";

export default ButtonClient; 
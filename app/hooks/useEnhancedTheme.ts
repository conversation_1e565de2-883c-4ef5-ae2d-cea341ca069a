"use client";

import { useTheme } from '@/app/contexts/ThemeContext';
import { ThemeMode, ThemeVariant } from '@/app/types/theme.types';
import { cn } from '@/app/lib/utils';
import { useMemo } from 'react';

/**
 * Status types for consistent status color handling
 */
export type StatusType = 'success' | 'warning' | 'info' | 'error' | 'pending' | 'in-progress';

/**
 * Theme class variants for conditional styling
 */
export interface ThemeClassVariants {
  light?: string;
  dark?: string;
  [variant: string]: string | undefined;
}

/**
 * Enhanced theme hook that provides utilities and optimized theme access
 * Builds upon the existing ThemeContext with additional utility functions
 */
export const useEnhancedTheme = () => {
  const themeContext = useTheme();
  
  const utilities = useMemo(() => ({
    /**
     * Generate theme-aware class names with optional variants
     */
    getThemeClass: (baseClass: string, variants?: ThemeClassVariants) => {
      if (!variants) return baseClass;
      
      const { currentTheme } = themeContext;
      const modeVariant = variants[currentTheme.mode];
      const variantClass = variants[currentTheme.variant];
      
      return cn(baseClass, modeVariant, variantClass);
    },
    
    /**
     * Get standardized status color classes
     */
    getStatusColor: (status: StatusType) => {
      const statusMap = {
        success: 'bg-success/10 text-success border-success/20',
        warning: 'bg-warning/10 text-warning border-warning/20',
        info: 'bg-info/10 text-info border-info/20',
        error: 'bg-destructive/10 text-destructive border-destructive/20',
        pending: 'bg-muted/10 text-muted-foreground border-muted/20',
        'in-progress': 'bg-info/10 text-info border-info/20',
      };
      
      return statusMap[status] || statusMap.info;
    },
    
    /**
     * Check if current theme matches specified variant and mode
     */
    isCurrentTheme: (variant: ThemeVariant, mode: ThemeMode) => {
      const { currentTheme } = themeContext;
      return currentTheme.variant === variant && currentTheme.mode === mode;
    },
    
    /**
     * Performance optimized theme class maps
     */
    themeClasses: {
      // Card components
      card: 'bg-card text-card-foreground border-border shadow-sm',
      cardElevated: 'bg-card text-card-foreground border-border shadow-lg',
      cardInteractive: 'bg-card text-card-foreground border-border shadow-sm hover:shadow-md cursor-pointer hover:bg-accent/5',
      
      // Button variants
      button: {
        primary: 'bg-primary text-primary-foreground hover:bg-primary/90 focus:ring-2 focus:ring-ring focus:ring-offset-2',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 focus:ring-2 focus:ring-ring focus:ring-offset-2',
        ghost: 'hover:bg-accent hover:text-accent-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground focus:ring-2 focus:ring-ring focus:ring-offset-2',
      },
      
      // Input components
      input: 'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
      
      // Interactive elements
      interactive: 'hover:bg-accent/50 focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-200',
      
      // Text variants
      text: {
        primary: 'text-foreground',
        secondary: 'text-muted-foreground',
        accent: 'text-accent-foreground',
        muted: 'text-muted-foreground',
      },
      
      // Background variants
      background: {
        default: 'bg-background',
        card: 'bg-card',
        muted: 'bg-muted',
        accent: 'bg-accent',
        primary: 'bg-primary',
        secondary: 'bg-secondary',
      },
      
      // Border variants
      border: {
        default: 'border-border',
        input: 'border-input',
        muted: 'border-muted',
      },
    },
    
    /**
     * Status color mappings for consistent status handling
     */
    statusColors: {
      success: {
        background: 'bg-success/10',
        text: 'text-success',
        border: 'border-success/20',
        full: 'bg-success/10 text-success border-success/20',
      },
      warning: {
        background: 'bg-warning/10',
        text: 'text-warning',
        border: 'border-warning/20',
        full: 'bg-warning/10 text-warning border-warning/20',
      },
      info: {
        background: 'bg-info/10',
        text: 'text-info',
        border: 'border-info/20',
        full: 'bg-info/10 text-info border-info/20',
      },
      error: {
        background: 'bg-destructive/10',
        text: 'text-destructive',
        border: 'border-destructive/20',
        full: 'bg-destructive/10 text-destructive border-destructive/20',
      },
      pending: {
        background: 'bg-muted/10',
        text: 'text-muted-foreground',
        border: 'border-muted/20',
        full: 'bg-muted/10 text-muted-foreground border-muted/20',
      },
      'in-progress': {
        background: 'bg-info/10',
        text: 'text-info',
        border: 'border-info/20',
        full: 'bg-info/10 text-info border-info/20',
      },
    },
  }), [themeContext]);
  
  return {
    ...themeContext,
    ...utilities,
  };
};

/**
 * Hook for consistent theme class generation
 * Provides commonly used class combinations
 */
export const useThemeClasses = () => {
  const { themeClasses } = useEnhancedTheme();
  return themeClasses;
};

/**
 * Hook for accessing status colors
 * Provides standardized status color classes
 */
export const useStatusColors = () => {
  const { statusColors, getStatusColor } = useEnhancedTheme();
  return {
    statusColors,
    getStatusColor,
  };
};

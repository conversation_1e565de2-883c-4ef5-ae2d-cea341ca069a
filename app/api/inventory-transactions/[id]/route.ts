import connectToDatabase from '@/app/lib/mongodb';
import { ObjectId } from 'mongodb';
import { NextRequest, NextResponse } from 'next/server';

/**
 * GET handler for a specific inventory transaction
 * Fetches a single inventory transaction by ID
 */
export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const connection = await connectToDatabase();
    const db = connection.db;
    console.log('[API] Database connection established:', !!db);
    const resolvedParams = await params;
    const id = resolvedParams.id;

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    const transaction = await db.collection('transactions').findOne({
      _id: new ObjectId(id)
    });

    if (!transaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Fetch part details
    if (transaction.partId && ObjectId.isValid(transaction.partId)) {
      const part = await db.collection('parts').findOne({
        _id: new ObjectId(transaction.partId)
      });

      if (part) {
        transaction.part = part;
      }
    }

    return NextResponse.json({
      success: true,
      data: transaction
    });
  } catch (error) {
    console.error(`Error fetching transaction:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch transaction' },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for a specific inventory transaction
 * Updates an existing inventory transaction
 */
export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const connection = await connectToDatabase();
    const db = connection.db;
    console.log('[API] Database connection established:', !!db);
    const resolvedParams = await params;
    const id = resolvedParams.id;
    const data = await request.json() as {
      notes?: string;
      referenceNumber?: string;
      [key: string]: any;
    };

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    // Find the existing transaction
    const existingTransaction = await db.collection('transactions').findOne({
      _id: new ObjectId(id)
    });

    if (!existingTransaction) {
      return NextResponse.json(
        { success: false, error: 'Transaction not found' },
        { status: 404 }
      );
    }

    // Only allow updating notes and reference number
    const updateData = {
      notes: data.notes,
      referenceNumber: data.referenceNumber
    };

    const result = await db.collection('transactions').updateOne(
      { _id: new ObjectId(id) },
      { $set: updateData }
    );

    return NextResponse.json({
      success: true,
      data: { ...existingTransaction, ...updateData }
    });
  } catch (error) {
    console.error(`Error updating transaction:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to update transaction' },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for a specific inventory transaction
 * DISABLED: Transactions are immutable in event-sourced inventory system
 */
export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const resolvedParams = await params;
    const id = resolvedParams.id;

    // Log the deletion attempt for audit purposes
    console.warn(`[API] Attempted deletion of transaction ${id} - BLOCKED: Transactions are immutable`);

    if (!ObjectId.isValid(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid transaction ID' },
        { status: 400 }
      );
    }

    // Return 405 Method Not Allowed with clear explanation
    return NextResponse.json(
      {
        success: false,
        error: 'Transaction deletion is not allowed. Inventory transactions are immutable for audit integrity. To correct errors, create a reversing transaction instead.',
        code: 'TRANSACTION_IMMUTABLE',
        guidance: 'Use the stock adjustment feature to create a correcting transaction that reverses the effect of this transaction.'
      },
      {
        status: 405,
        headers: {
          'Allow': 'GET, PUT',
          'X-Deprecation-Notice': 'Transaction deletion disabled for data integrity'
        }
      }
    );
  } catch (error) {
    console.error(`Error processing transaction deletion request:`, error);
    return NextResponse.json(
      { success: false, error: 'Failed to process request' },
      { status: 500 }
    );
  }
}
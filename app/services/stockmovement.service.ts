import mongoose, { ClientSession, Types } from 'mongoose';
import connectToDatabase, { handleMongoDBError } from '@/app/lib/mongodb';
import Part, { IPartDocument, IStockLevels } from '@/app/models/part.model';
import InventoryTransaction, { IInventoryTransaction, IMovementLocation } from '@/app/models/inventorytransaction.model';
import { Inventories } from '@/app/models/inventories.model';
import InventoriesService, { StockOperationParams, executeAtomicStockTransfer } from './inventory.service';
import { generateTransactionId } from './transactionIdGenerator';
import { captureException, setTag } from '../lib/logging-utils';

// Logger function for tracking database operations
const logOperation = (operation: string, entity: string, details?: any) => {
  const timestamp = new Date().toISOString();
  console.log(`[StockMovementService][${timestamp}] ${operation} on ${entity}${details ? ': ' + JSON.stringify(details) : ''}`);
};

// Stock movement types for business logic
export type StockMovementType =
  | 'purchase_receipt'    // External → Raw stock
  | 'internal_transfer'   // Between warehouses/stock types
  | 'sales_shipment'      // Finished stock → External
  | 'scrap_disposal'      // Rejected stock → Scrap
  | 'process_move'        // Between manufacturing states
  | 'adjustment';         // Manual stock adjustment

// Interface for stock movement request
export interface StockMovementRequest {
  partId: string;
  movementType: StockMovementType;
  quantity: number;
  from?: {
    warehouseId: string;
    locationId?: string; // NEW: Specific location within warehouse
    stockType: keyof IStockLevels;
  } | null;
  to?: {
    warehouseId: string;
    locationId?: string; // NEW: Specific location within warehouse
    stockType: keyof IStockLevels | 'scrap';
  } | null;
  userId: string;
  referenceNumber?: string;
  referenceType?: 'PurchaseOrder' | 'WorkOrder' | 'SalesOrder' | 'StockAdjustment' | 'ProcessOrder';
  notes?: string;
  transactionDate?: Date;
}

// Interface for stock movement result
export interface StockMovementResult {
  success: boolean;
  transaction: IInventoryTransaction;
  updatedPart: IPartDocument;
  message: string;
}

/**
 * Atomic stock movement service that updates part inventory and creates transaction log
 * in a single MongoDB transaction to ensure data consistency
 */
export class StockMovementService {
  
  /**
   * Execute a stock movement with full atomicity using inventories collection
   */
  static async executeMovement(request: StockMovementRequest): Promise<StockMovementResult> {
    await connectToDatabase();

    const session = await mongoose.startSession();

    try {
      logOperation('executeMovement', 'service', {
        partId: request.partId.toString(),
        quantity: request.quantity,
        movementType: request.movementType,
        from: request.from,
        to: request.to
      });

      const result = await session.withTransaction(async () => {
        // Validate request
        await this.validateMovementRequest(request);

        // Get the part and validate it exists
        const part = await Part.findById(request.partId).session(session);
        if (!part) {
          throw new Error(`Part with ID ${request.partId} not found`);
        }

        // Apply business rules and determine actual from/to locations
        const { from, to } = this.applyBusinessRules(request, part);

        // Create transaction record first
        const transaction = await this.createTransactionRecord(request, from, to, session);

        // Apply inventory changes using inventories collection
        await this.applyInventoryChanges(request, from, to, session);

        logOperation('executeMovement completed', 'service', {
          transactionId: transaction.transactionId,
          partId: request.partId.toString()
        });

        return {
          success: true,
          transaction,
          updatedPart: part, // Part document is not directly updated in new schema
          message: `Successfully moved ${request.quantity} units`
        };
      });

      return result;
    } catch (error: any) {
      logOperation('executeMovement failed', 'service', { error: error.message });
      const errorMessage = handleMongoDBError(error);
      throw new Error(`Stock movement failed: ${errorMessage}`);
    } finally {
      await session.endSession();
    }
  }
  
  /**
   * Apply inventory changes using the inventories collection
   */
  private static async applyInventoryChanges(
    request: StockMovementRequest,
    from: IMovementLocation | null,
    to: IMovementLocation | null,
    session: ClientSession
  ): Promise<void> {
    const { partId, quantity, movementType } = request;

    switch (movementType) {
      case 'purchase_receipt':
        // External receipt - only increment destination
        if (!to) {
          throw new Error('Purchase receipt requires destination (to) location');
        }
        // Use location-based inventory update if locationId is provided
        if (to.locationId) {
          await this.incrementLocationStock({
            partId: new Types.ObjectId(partId),
            locationId: to.locationId,
            stockType: this.mapStockType(to.stockType),
            quantity,
            session
          });
        } else if (to.warehouseId) {
          // Fallback to warehouse-based for backward compatibility
          await InventoriesService.incrementStock({
            partId: new Types.ObjectId(partId),
            warehouseId: to.warehouseId,
            stockType: this.mapStockType(to.stockType),
            quantity,
            session
          });
        } else {
          throw new Error('Either locationId or warehouseId must be provided for destination');
        }
        break;

      case 'sales_shipment':
      case 'scrap_disposal':
        // External shipment - only decrement source
        if (!from) {
          throw new Error(`${movementType} requires source (from) location`);
        }
        // Use location-based inventory update if locationId is provided
        if (from.locationId) {
          await this.decrementLocationStock({
            partId: new Types.ObjectId(partId),
            locationId: from.locationId,
            stockType: this.mapStockType(from.stockType),
            quantity,
            session
          });
        } else if (from.warehouseId) {
          // Fallback to warehouse-based for backward compatibility
          await InventoriesService.decrementStock({
            partId: new Types.ObjectId(partId),
            warehouseId: from.warehouseId,
            stockType: this.mapStockType(from.stockType),
            quantity,
            session
          });
        } else {
          throw new Error('Either locationId or warehouseId must be provided for source');
        }
        break;

      case 'internal_transfer':
      case 'process_move':
        // Internal movement - atomic transfer between locations
        if (!from || !to) {
          throw new Error(`${movementType} requires both source (from) and destination (to) locations`);
        }

        // Use location-based transfer if both locations are specified
        if (from.locationId && to.locationId) {
          // Atomic location-to-location transfer
          await this.decrementLocationStock({
            partId: new Types.ObjectId(partId),
            locationId: from.locationId,
            stockType: this.mapStockType(from.stockType),
            quantity,
            session
          });

          await this.incrementLocationStock({
            partId: new Types.ObjectId(partId),
            locationId: to.locationId,
            stockType: this.mapStockType(to.stockType),
            quantity,
            session
          });
        } else if (from.warehouseId && to.warehouseId) {
          // Fallback to warehouse-based transfer for backward compatibility
          await executeAtomicStockTransfer(
            {
              partId: new Types.ObjectId(partId),
              warehouseId: from.warehouseId,
              stockType: this.mapStockType(from.stockType),
              quantity,
              session
            },
            {
              partId: new Types.ObjectId(partId),
              warehouseId: to.warehouseId,
              stockType: this.mapStockType(to.stockType),
              quantity,
              session
            }
          );
        } else {
          throw new Error('Either locationId or warehouseId must be provided for both source and destination');
        }
        break;

      case 'adjustment':
        // Manual adjustment - can be increment or decrement
        if (to) {
          // Use location-based adjustment if locationId is provided
          if (to.locationId) {
            await this.incrementLocationStock({
              partId: new Types.ObjectId(partId),
              locationId: to.locationId,
              stockType: this.mapStockType(to.stockType),
              quantity,
              session
            });
          } else if (to.warehouseId) {
            // Fallback to warehouse-based for backward compatibility
            await InventoriesService.incrementStock({
              partId: new Types.ObjectId(partId),
              warehouseId: to.warehouseId,
              stockType: this.mapStockType(to.stockType),
              quantity,
              session
            });
          } else {
            throw new Error('Either locationId or warehouseId must be provided for adjustment destination');
          }
        }
        if (from && from.warehouseId) {
          await InventoriesService.decrementStock({
            partId: new Types.ObjectId(partId),
            warehouseId: from.warehouseId,
            stockType: this.mapStockType(from.stockType),
            quantity,
            session
          });
        }
        break;

      default:
        throw new Error(`Unsupported movement type: ${movementType}`);
    }
  }

  /**
   * Map stock type to inventories collection format
   */
  private static mapStockType(stockType: string): 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected' {
    const validStockTypes = ['raw', 'hardening', 'grinding', 'finished', 'rejected'];
    if (!validStockTypes.includes(stockType)) {
      throw new Error(`Invalid stock type: ${stockType}`);
    }
    return stockType as 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
  }

  /**
   * Validate the movement request
   */
  private static async validateMovementRequest(request: StockMovementRequest): Promise<void> {
    // Validate ObjectIds
    if (!Types.ObjectId.isValid(request.partId)) {
      throw new Error('Invalid part ID format');
    }
    if (!Types.ObjectId.isValid(request.userId)) {
      throw new Error('Invalid user ID format');
    }
    
    // Validate quantity
    if (!Number.isInteger(request.quantity) || request.quantity <= 0) {
      throw new Error('Quantity must be a positive integer');
    }
    
    // Validate warehouse IDs if provided
    if (request.from?.warehouseId && !Types.ObjectId.isValid(request.from.warehouseId)) {
      throw new Error('Invalid from warehouse ID format');
    }
    if (request.to?.warehouseId && !Types.ObjectId.isValid(request.to.warehouseId)) {
      throw new Error('Invalid to warehouse ID format');
    }

    // Validate location IDs if provided
    if (request.from?.locationId && !Types.ObjectId.isValid(request.from.locationId)) {
      throw new Error('Invalid from location ID format');
    }
    if (request.to?.locationId && !Types.ObjectId.isValid(request.to.locationId)) {
      throw new Error('Invalid to location ID format');
    }
  }
  
  /**
   * Apply business rules to determine actual from/to locations
   */
  private static applyBusinessRules(
    request: StockMovementRequest, 
    part: IPartDocument
  ): { from: IMovementLocation | null; to: IMovementLocation | null } {
    
    let from: IMovementLocation | null = null;
    let to: IMovementLocation | null = null;
    
    switch (request.movementType) {
      case 'purchase_receipt':
        // External purchase → Raw stock
        from = null; // External source
        to = {
          ...(request.to?.locationId && { locationId: new Types.ObjectId(request.to.locationId) }),
          warehouseId: new Types.ObjectId(request.to?.warehouseId || part.inventory?.warehouseId),
          stockType: 'raw'
        };
        break;
        
      case 'sales_shipment':
        // Finished stock → External customer
        from = {
          ...(request.from?.locationId && { locationId: new Types.ObjectId(request.from.locationId) }),
          warehouseId: new Types.ObjectId(request.from?.warehouseId || part.inventory?.warehouseId),
          stockType: 'finished'
        };
        to = null; // External destination
        break;
        
      case 'scrap_disposal':
        // Rejected stock → Scrap
        from = {
          ...(request.from?.locationId && { locationId: new Types.ObjectId(request.from.locationId) }),
          warehouseId: new Types.ObjectId(request.from?.warehouseId || part.inventory?.warehouseId),
          stockType: 'rejected'
        };
        to = {
          ...(request.to?.locationId && { locationId: new Types.ObjectId(request.to.locationId) }),
          warehouseId: new Types.ObjectId(request.to?.warehouseId || part.inventory?.warehouseId),
          stockType: 'scrap' as any // Special case for scrap
        };
        break;
        
      case 'internal_transfer':
      case 'process_move':
      case 'adjustment':
        // Use provided from/to locations
        if (request.from) {
          from = {
            ...(request.from.locationId && { locationId: new Types.ObjectId(request.from.locationId) }),
            warehouseId: new Types.ObjectId(request.from.warehouseId),
            stockType: request.from.stockType
          };
        }
        if (request.to) {
          to = {
            ...(request.to.locationId && { locationId: new Types.ObjectId(request.to.locationId) }),
            warehouseId: new Types.ObjectId(request.to.warehouseId),
            stockType: request.to.stockType as any
          };
        }
        break;
    }
    
    return { from, to };
  }

  
  /**
   * Create transaction record
   */
  private static async createTransactionRecord(
    request: StockMovementRequest,
    from: IMovementLocation | null,
    to: IMovementLocation | null,
    session: ClientSession
  ): Promise<IInventoryTransaction> {
    
    const transactionId = await generateTransactionId('MOV');
    
    const transactionData = {
      transactionId,
      partId: new Types.ObjectId(request.partId),
      itemType: 'Part' as const,
      from,
      to,
      quantity: request.quantity,
      transactionType: request.movementType,
      transactionDate: request.transactionDate || new Date(),
      referenceNumber: request.referenceNumber || null,
      referenceType: request.referenceType || null,
      userId: new Types.ObjectId(request.userId),
      notes: request.notes || null,
      
      // Backward compatibility fields (deprecated)
      warehouseId: from?.warehouseId || to?.warehouseId || null,
      previousStock: undefined, // Not applicable in event-sourced model
      newStock: undefined // Not applicable in event-sourced model
    };
    
    const transaction = new InventoryTransaction(transactionData);
    await transaction.save({ session });
    
    return transaction;
  }

  /**
   * Execute purchase receipt
   */
  static async executePurchaseReceipt(params: {
    partId: string;
    warehouseId: string;
    stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
    quantity: number;
    userId: string;
    referenceNumber?: string;
    notes?: string;
  }): Promise<StockMovementResult> {
    return this.executeMovement({
      partId: params.partId,
      movementType: 'purchase_receipt',
      quantity: params.quantity,
      from: null,
      to: { locationId: params.warehouseId, warehouseId: params.warehouseId, stockType: params.stockType
       },
      userId: params.userId,
      referenceNumber: params.referenceNumber || '',
      referenceType: 'PurchaseOrder',
      notes: params.notes || ''
    });
  }

  /**
   * Execute internal transfer
   */
  static async executeInternalTransfer(params: {
    partId: string;
    fromWarehouseId: string;
    fromStockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
    toWarehouseId: string;
    toStockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
    quantity: number;
    userId: string;
    referenceNumber?: string;
    notes?: string;
  }): Promise<StockMovementResult> {
    return this.executeMovement({
      partId: params.partId,
      movementType: 'internal_transfer',
      quantity: params.quantity,
      from: { locationId: params.fromWarehouseId, warehouseId: params.fromWarehouseId, stockType: params.fromStockType
       },
      to: { locationId: params.toWarehouseId, warehouseId: params.toWarehouseId, stockType: params.toStockType
       },
      userId: params.userId,
      referenceNumber: params.referenceNumber || '',
      referenceType: 'WorkOrder',
      notes: params.notes || ''
    });
  }

  /**
   * Execute sales shipment
   */
  static async executeSalesShipment(params: {
    partId: string;
    warehouseId: string;
    stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
    quantity: number;
    userId: string;
    referenceNumber?: string;
    notes?: string;
  }): Promise<StockMovementResult> {
    return this.executeMovement({
      partId: params.partId,
      movementType: 'sales_shipment',
      quantity: params.quantity,
      from: { locationId: params.warehouseId, warehouseId: params.warehouseId, stockType: params.stockType
       },
      to: null,
      userId: params.userId,
      referenceNumber: params.referenceNumber || '',
      referenceType: 'SalesOrder',
      notes: params.notes || ''
    });
  }

  /**
   * Increment stock at a specific location
   */
  private static async incrementLocationStock(params: {
    partId: Types.ObjectId;
    locationId: Types.ObjectId;
    stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
    quantity: number;
    session: ClientSession;
  }): Promise<void> {
    const { partId, locationId, stockType, quantity } = params;

    // Use the unified inventory service for location-based updates
    const { updateInventoryQuantity } = await import('./inventory.service');

    // Get current quantity
    const currentInventory = await Inventories.findOne({
      partId,
      locationId,
      stockType
    }).session(params.session);

    const currentQuantity = currentInventory?.quantity || 0;
    const newQuantity = currentQuantity + quantity;

    await updateInventoryQuantity({
      partId: partId.toString(),
      locationId: locationId.toString(),
      stockType,
      quantity: newQuantity
    });
  }

  /**
   * Decrement stock at a specific location
   */
  private static async decrementLocationStock(params: {
    partId: Types.ObjectId;
    locationId: Types.ObjectId;
    stockType: 'raw' | 'hardening' | 'grinding' | 'finished' | 'rejected';
    quantity: number;
    session: ClientSession;
  }): Promise<void> {
    const { partId, locationId, stockType, quantity } = params;

    // Get current quantity
    const currentInventory = await Inventories.findOne({
      partId,
      locationId,
      stockType
    }).session(params.session);

    const currentQuantity = currentInventory?.quantity || 0;

    if (currentQuantity < quantity) {
      throw new Error(`Insufficient stock. Available: ${currentQuantity}, Required: ${quantity}`);
    }

    const newQuantity = currentQuantity - quantity;

    // Use the new inventories-v2 service for location-based updates
    const { updateInventoryQuantity } = await import('./inventory.service');

    await updateInventoryQuantity({
      partId: partId.toString(),
      locationId: locationId.toString(),
      stockType,
      quantity: newQuantity
    });
  }
}

export default StockMovementService;
